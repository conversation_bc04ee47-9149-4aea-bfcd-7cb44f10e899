const { spawn } = require('child_process')
const { createServer } = require('vite')
const path = require('path')

// 智能启动脚本 - 自动检测并启动开发环境
async function startElectronDev() {
  console.log('🚀 启动 Electron 开发环境...')

  try {
    // 1. 检查是否已有 Vite 服务器运行
    let vitePort = 5173
    let isViteRunning = await checkViteServer('http://localhost:5173')

    if (!isViteRunning) {
      // 检查 5174 端口
      isViteRunning = await checkViteServer('http://localhost:5174')
      if (isViteRunning) {
        vitePort = 5174
      }
    }

    if (!isViteRunning) {
      console.log('❌ 未检测到 Vite 开发服务器')
      console.log('💡 请先在另一个终端运行: npm run dev')
      console.log('💡 然后再运行: npm run electron:smart')
      process.exit(1)
    } else {
      console.log(`✅ Vite 开发服务器已运行在端口 ${vitePort}`)
    }

    // 2. 等待服务器就绪
    await waitForServer(`http://localhost:${vitePort}`, 10000)

    // 3. 启动 Electron
    console.log('⚡ 启动 Electron 应用...')
    startElectron()

  } catch (error) {
    console.error('❌ 启动失败:', error.message)
    process.exit(1)
  }
}

// 检查 Vite 服务器是否运行
async function checkViteServer(url = 'http://localhost:5173') {
  try {
    const response = await fetch(url)
    return response.ok
  } catch (error) {
    return false
  }
}

// 启动 Vite 开发服务器
async function startViteServer() {
  return new Promise((resolve, reject) => {
    const viteProcess = spawn('npm', ['run', 'dev'], {
      stdio: 'pipe',
      shell: true,
      cwd: __dirname
    })

    let serverReady = false
    let detectedPort = 5173

    viteProcess.stdout.on('data', (data) => {
      const output = data.toString()
      console.log(output)

      // 检测服务器启动成功和端口
      if (output.includes('Local:')) {
        const portMatch = output.match(/localhost:(\d+)/)
        if (portMatch) {
          detectedPort = parseInt(portMatch[1])
          serverReady = true
          resolve(detectedPort)
        }
      }
    })
    
    viteProcess.stderr.on('data', (data) => {
      console.error('Vite Error:', data.toString())
    })
    
    viteProcess.on('close', (code) => {
      if (!serverReady) {
        reject(new Error(`Vite 服务器启动失败，退出码: ${code}`))
      }
    })
    
    // 超时处理
    setTimeout(() => {
      if (!serverReady) {
        viteProcess.kill()
        reject(new Error('Vite 服务器启动超时'))
      }
    }, 30000)
  })
}

// 等待服务器就绪
async function waitForServer(url, timeout = 30000) {
  const startTime = Date.now()
  
  while (Date.now() - startTime < timeout) {
    try {
      const response = await fetch(url)
      if (response.ok) {
        console.log('✅ 服务器就绪:', url)
        return true
      }
    } catch (error) {
      // 继续等待
    }
    
    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  
  throw new Error(`服务器启动超时: ${url}`)
}

// 启动 Electron
function startElectron() {
  const electronProcess = spawn('electron', ['.'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  })
  
  electronProcess.on('close', (code) => {
    console.log(`Electron 进程退出，退出码: ${code}`)
    process.exit(code)
  })
  
  // 处理进程终止
  process.on('SIGINT', () => {
    console.log('收到终止信号，关闭 Electron...')
    electronProcess.kill('SIGINT')
  })
  
  process.on('SIGTERM', () => {
    console.log('收到终止信号，关闭 Electron...')
    electronProcess.kill('SIGTERM')
  })
}

// 全局 fetch polyfill for Node.js < 18
if (!global.fetch) {
  global.fetch = require('node-fetch')
}

// 启动应用
startElectronDev().catch(error => {
  console.error('❌ 启动失败:', error)
  process.exit(1)
})
