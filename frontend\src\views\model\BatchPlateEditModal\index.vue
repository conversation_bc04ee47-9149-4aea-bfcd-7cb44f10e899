<template>
  <NModal
    :show="visible"
    preset="card"
    title="岗亭批量修改车牌"
    :mask-closable="false"
    :closable="true"
    style="width: 95vw; height: 90vh;"
    @update:show="handleUpdateVisible"
    @close="handleClose"
  >
    <!-- 搜索条件区域 -->
    <div class="search-section">
      <div class="search-row">
        <div class="search-item">
          <span class="search-label">车牌号码</span>
          <NInput
            :value="searchForm.plateNumber"
            @update:value="searchForm.plateNumber = $event"
            placeholder=""
            style="width: 150px;"
            size="small"
          />
        </div>

        <div class="search-item">
          <NDatePicker
            :value="searchForm.dateRange"
            @update:value="searchForm.dateRange = $event"
            type="datetimerange"
            format="yyyy-MM-dd HH:mm:ss"
            style="width: 300px;"
            size="small"
            :default-value="[new Date('2025-06-22 17:47:33'), new Date('2025-07-22 17:47:33')]"
          />
        </div>

        <div class="search-item">
          <NSelect
            :value="searchForm.plateColor"
            @update:value="searchForm.plateColor = $event"
            :options="plateColorOptions"
            style="width: 120px;"
            size="small"
          />
        </div>

        <div class="search-item">
          <NSelect
            :value="searchForm.entryChannel"
            @update:value="searchForm.entryChannel = $event"
            :options="entryChannelOptions"
            style="width: 120px;"
            size="small"
          />
        </div>

        <div class="search-item">
          <NCheckbox :checked="searchForm.includeEmpty" @update:checked="searchForm.includeEmpty = $event">
            空车牌
          </NCheckbox>
        </div>

        <div class="search-actions">
          <NButton type="primary" @click="handleSearch" :loading="searching" size="small">
            搜索
          </NButton>
          <NButton @click="handleReset" size="small">
            重置
          </NButton>
        </div>
      </div>
    </div>

    <!-- 车牌图片网格区域 -->
    <div class="plate-grid-section">
      <div class="plate-grid">
        <div
          v-for="(plate, index) in plateList"
          :key="index"
          class="plate-item"
        >
          <!-- 车牌类型标签 -->
          <div class="plate-type-label">{{ plate.vehicleType }}</div>

          <!-- 车牌图片区域 -->
          <div class="plate-image-container">
            <img
              v-if="plate.imageUrl"
              :src="plate.imageUrl"
              :alt="plate.plateNumber"
              class="plate-image"
            />
            <div v-else class="no-image">
              <div class="no-image-icon">📷</div>
            </div>

            <!-- 车牌号码标签 -->
            <div class="plate-number-overlay">
              {{ plate.plateNumber || '未识别' }}
            </div>
          </div>

          <!-- 车牌信息区域 -->
          <div class="plate-info-section">
            <div class="plate-info-row">
              <span class="info-label">车</span>
              <span class="info-value">{{ plate.plateNumber || 'AA10005' }}</span>
              <NSelect
                :value="plate.plateColor"
                @update:value="updatePlateColor(index, $event)"
                :options="plateColorOptions"
                size="small"
                style="width: 60px; margin-left: auto;"
              />
            </div>

            <div class="plate-time-row">
              {{ formatTime(plate.captureTime) }}
              <NButton
                size="small"
                type="primary"
                @click="editPlate(index)"
                style="margin-left: auto;"
              >
                修改
              </NButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <div class="pagination-controls">
        <NButton
          size="small"
          :disabled="currentPage <= 1"
          @click="currentPage = currentPage - 1"
          class="page-btn"
        >
          &lt;
        </NButton>

        <NButton
          v-for="page in visiblePages"
          :key="page"
          size="small"
          :class="['page-btn', { active: page === currentPage }]"
          @click="currentPage = page"
        >
          {{ page }}
        </NButton>

        <NButton
          size="small"
          :disabled="currentPage >= totalPages"
          @click="currentPage = currentPage + 1"
          class="page-btn"
        >
          &gt;
        </NButton>
      </div>
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <div class="selected-info">
          已选择 {{ selectedPlates.length }} 个车牌
        </div>
        <div class="footer-actions">
          <NButton size="large" @click="handleClose">
            关闭
          </NButton>
          <NButton
            type="primary"
            size="large"
            :disabled="selectedPlates.length === 0"
            :loading="saving"
            @click="handleBatchEdit"
          >
            批量修改
          </NButton>
        </div>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { 
  NModal, 
  NButton, 
  NInput, 
  NDatePicker, 
  NSelect, 
  NCheckbox, 
  NPagination,
  NIcon
} from 'naive-ui'

defineOptions({
  name: 'BatchPlateEditModal'
})

// ==================== Props & Emits ====================
interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'batchEdit', data: { plateIds: number[]; newPlateNumber: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const searching = ref(false)
const saving = ref(false)
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const selectedPlates = ref<number[]>([])

// 搜索表单
const searchForm = reactive({
  plateNumber: '',
  dateRange: null as [number, number] | null,
  plateColor: 'all',
  entryChannel: 'all',
  includeEmpty: false
})

// 选项数据
const plateColorOptions = [
  { label: '全部', value: 'all' },
  { label: '蓝牌', value: 'blue' },
  { label: '黄牌', value: 'yellow' },
  { label: '绿牌', value: 'green' },
  { label: '白牌', value: 'white' }
]

const entryChannelOptions = [
  { label: '全部', value: 'all' },
  { label: '入口1', value: 'entry1' },
  { label: '入口2', value: 'entry2' },
  { label: '入口3', value: 'entry3' }
]

// 车牌列表数据
const plateList = ref([
  {
    id: 1,
    plateNumber: 'TT7000',
    imageUrl: '/api/placeholder/200/120',
    captureTime: new Date('2025-07-22 17:46:08'),
    plateColor: 'blue',
    channel: 'entry1',
    vehicleType: '普通车辆'
  },
  {
    id: 2,
    plateNumber: 'TT2221',
    imageUrl: '/api/placeholder/200/120',
    captureTime: new Date('2025-07-22 14:08:26'),
    plateColor: 'blue',
    channel: 'entry1',
    vehicleType: '普通车辆'
  },
  {
    id: 3,
    plateNumber: 'MX8549',
    imageUrl: '/api/placeholder/200/120',
    captureTime: new Date('2025-07-22 14:00:06'),
    plateColor: 'blue',
    channel: 'entry2',
    vehicleType: '普通车辆'
  },
  {
    id: 4,
    plateNumber: 'MK3226',
    imageUrl: '/api/placeholder/200/120',
    captureTime: new Date('2025-07-22 11:37:47'),
    plateColor: 'blue',
    channel: 'entry2',
    vehicleType: '普通车辆'
  },
  {
    id: 5,
    plateNumber: 'AA10005',
    imageUrl: null,
    captureTime: new Date('2025-07-21 15:08:04'),
    plateColor: 'blue',
    channel: 'entry3',
    vehicleType: '普通车辆'
  },
  {
    id: 6,
    plateNumber: 'MZ1234',
    imageUrl: '/api/placeholder/200/120',
    captureTime: new Date('2025-07-21 14:43:40'),
    plateColor: 'blue',
    channel: 'entry1',
    vehicleType: '内部车辆'
  },
  {
    id: 7,
    plateNumber: 'MR1234',
    imageUrl: '/api/placeholder/200/120',
    captureTime: new Date('2025-07-21 14:28:58'),
    plateColor: 'blue',
    channel: 'entry2',
    vehicleType: '内部车辆'
  },
  {
    id: 8,
    plateNumber: 'MP1234',
    imageUrl: '/api/placeholder/200/120',
    captureTime: new Date('2025-07-21 14:23:08'),
    plateColor: 'blue',
    channel: 'entry3',
    vehicleType: '内部车辆'
  },
  {
    id: 9,
    plateNumber: 'AA10072',
    imageUrl: null,
    captureTime: new Date('2025-07-21 09:57:13'),
    plateColor: 'blue',
    channel: 'entry1',
    vehicleType: '内部车辆'
  },
  {
    id: 10,
    plateNumber: 'AA10011',
    imageUrl: null,
    captureTime: new Date('2025-07-21 09:56:56'),
    plateColor: 'blue',
    channel: 'entry2',
    vehicleType: '内部车辆'
  }
])

// ==================== 计算属性 ====================
const totalPages = computed(() => Math.ceil(totalCount.value / pageSize.value))

const visiblePages = computed(() => {
  const pages = []
  const start = Math.max(1, currentPage.value - 2)
  const end = Math.min(totalPages.value, currentPage.value + 2)

  for (let i = start; i <= end; i++) {
    pages.push(i)
  }

  return pages
})

// ==================== 方法 ====================
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleSearch = async () => {
  searching.value = true
  try {
    // 模拟搜索API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    totalCount.value = plateList.value.length
    console.log('搜索条件:', searchForm)
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    searching.value = false
  }
}

const handleReset = () => {
  searchForm.plateNumber = ''
  searchForm.dateRange = null
  searchForm.plateColor = 'all'
  searchForm.entryChannel = 'all'
  searchForm.includeEmpty = false
  selectedPlates.value = []
}

const togglePlateSelection = (index: number) => {
  const selectedIndex = selectedPlates.value.indexOf(index)
  if (selectedIndex > -1) {
    selectedPlates.value.splice(selectedIndex, 1)
  } else {
    selectedPlates.value.push(index)
  }
}

const editPlate = (index: number) => {
  console.log('编辑车牌:', plateList.value[index])
  // 这里可以打开单个车牌编辑弹框
}

const updatePlateColor = (index: number, color: string) => {
  plateList.value[index].plateColor = color
  console.log('更新车牌颜色:', plateList.value[index])
}

const handleBatchEdit = async () => {
  if (selectedPlates.value.length === 0) return
  
  saving.value = true
  try {
    // 模拟批量修改API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const selectedPlateIds = selectedPlates.value.map(index => plateList.value[index].id)
    emit('batchEdit', {
      plateIds: selectedPlateIds,
      newPlateNumber: 'NEW_PLATE' // 这里应该从输入框获取
    })
    
    emit('update:visible', false)
  } catch (error) {
    console.error('批量修改失败:', error)
  } finally {
    saving.value = false
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// ==================== 监听器 ====================
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置状态
    selectedPlates.value = []
    currentPage.value = 1
    totalCount.value = plateList.value.length
    handleSearch()
  }
})

watch([currentPage, pageSize], () => {
  if (props.visible) {
    handleSearch()
  }
})
</script>

<style scoped>
.search-section {
  margin-bottom: 16px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.search-label {
  font-size: 13px;
  color: #333;
  white-space: nowrap;
}

.search-actions {
  margin-left: auto;
  display: flex;
  gap: 8px;
}

.plate-grid-section {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 16px;
  max-height: calc(90vh - 200px);
}

.plate-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 12px;
  padding: 16px;
}

.plate-item {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  background: white;
  position: relative;
}

.plate-type-label {
  position: absolute;
  top: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 11px;
  z-index: 2;
}

.plate-image-container {
  position: relative;
  height: 100px;
  background: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.plate-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #ccc;
}

.no-image-icon {
  font-size: 24px;
  opacity: 0.5;
}

.plate-number-overlay {
  position: absolute;
  bottom: 4px;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 8px;
  border-radius: 2px;
  font-size: 11px;
  font-weight: bold;
}

.plate-info-section {
  padding: 8px;
  background: white;
}

.plate-info-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  font-size: 12px;
}

.info-label {
  color: #666;
  margin-right: 4px;
}

.info-value {
  font-weight: bold;
  color: #333;
}

.plate-time-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 11px;
  color: #666;
}

.pagination-section {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 12px 0;
  border-top: 1px solid #e0e0e0;
  gap: 8px;
}

.pagination-info {
  font-size: 12px;
  color: #666;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 4px;
}

.page-btn {
  min-width: 24px;
  height: 24px;
  padding: 0 6px;
  font-size: 12px;
}

.page-btn.active {
  background: #1890ff;
  color: white;
}
</style>
