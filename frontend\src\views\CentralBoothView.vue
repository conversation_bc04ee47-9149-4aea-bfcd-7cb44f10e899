<template>
  <div class="parking-management">
    <!-- Unified Top Bar -->
    <TopMenuBar
      status-label="在场车辆"
      :status-value="`${totalVehiclesInPark}辆`"
      :is-refreshing="isRefreshing"
      :current-booth="'central'"
      @refresh="refreshData"
      @toggleChannelList="toggleChannelList"
      @switchBooth="handleBoothSwitch"
    />

    <!-- Main Content Area -->
    <div class="main-content-area">
      <!-- 左侧浮动入口列表面板 -->
      <NDrawer v-model:show="showChannelList" :width="300" placement="left">
        <NDrawerContent title="监控列表" closable>
          <NList>
            <NListItem
              v-for="monitor in monitorList"
              :key="monitor.key"
              :class="{ 'n-list-item--active': selectedMonitor === monitor.key }"
              @click="selectMonitor(monitor.key)"
              style="cursor: pointer;"
            >
              <template #prefix>
                <span style="font-size: 18px; margin-right: 8px;">{{ monitor.icon }}</span>
              </template>
              {{ monitor.label }}
            </NListItem>
          </NList>
        </NDrawerContent>
      </NDrawer>

      <!-- Center Content Area - 中央岗亭布局 -->
      <div class="center-content-area central-booth">
        <!-- 顶部搜索和操作区域 -->
        <VehicleSearchBar
          @search="handleVehicleSearch"
          @ticket-entry="handleTicketEntry"
          @unrecognized="handleUnrecognized"
        />

        <!-- 监控图片网格区域 -->
        <div class="monitor-grid-section">
          <MonitorGrid
            ref="monitorGridRef"
            :data="monitorData"
            :page-size="6"
            @select="handleMonitorSelect"
            @double-click="handleMonitorDoubleClick"
          />
        </div>
      </div>

      <!-- Right Control Panel -->
      <ParkingFeePanel
        :selected-monitor-data="selectedMonitorData"
        @cash-payment-success="handleCashPaymentSuccess"
        @free-pass-click="handleFreePassClick"
      />

 
    </div>

    <!-- 大图弹框 -->
    <ImageModal
      :visible="showImageModal"
      :image-data="selectedImageData"
      @update:visible="showImageModal = $event"
    />

    <!-- 免费放行弹框 -->
    <FreePassModal
      :visible="showFreePassModal"
      @update:visible="showFreePassModal = $event"
      @confirm="handleFreePassConfirm"
      @cancel="handleFreePassCancel"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  NDrawer,
  NDrawerContent,
  NList,
  NListItem,
  NButton,
  useMessage
} from 'naive-ui'

// 组件导入
import TopMenuBar from '../components/TopMenuBar.vue'
import MonitorGrid from '../components/MonitorGrid/index.vue'
import ParkingFeePanel from '../components/ParkingFeePanel/index.vue'
import ImageModal from '../components/ImageModal/index.vue'
import FreePassModal from './model/FreePassModal/index.vue'
import VehicleSearchBar from '../components/VehicleSearchBar.vue'
import { mockMonitorData, type MonitorData } from '../data/mockMonitorData'

const router = useRouter()
const message = useMessage()

// 组件引用
const monitorGridRef = ref()

// 响应式数据
const isRefreshing = ref(false)
const showChannelList = ref(false)
const selectedMonitor = ref('overview')



// 统计数据
const totalVehiclesInPark = ref(156)

// 大图弹框状态
const showImageModal = ref(false)
const selectedImageData = ref<MonitorData | null>(null)

// 免费放行弹框状态
const showFreePassModal = ref(false)



// 监控列表
const monitorList = ref([
  { key: 'overview', label: '全景监控', icon: '🌐' },
  { key: 'area1', label: '私家车区域', icon: '🚗' },
  { key: 'area2', label: '电单车区域', icon: '🛵' },
  { key: 'area3', label: '地库监控', icon: '🏢' },
  { key: 'entrance', label: '入口监控', icon: '🚪' },
  { key: 'exit', label: '出口监控', icon: '🚪' }
])

// 监控数据
const monitorData = ref<MonitorData[]>(mockMonitorData)
const selectedMonitorData = ref<MonitorData | null>(null)





// 方法
const refreshData = () => {
  isRefreshing.value = true
  console.log('刷新中央岗亭数据...')

  setTimeout(() => {
    // 模拟数据更新
    totalVehiclesInPark.value = Math.floor(Math.random() * 200) + 100

    isRefreshing.value = false
    console.log('中央岗亭数据刷新完成')
  }, 2000)
}

const toggleChannelList = () => {
  showChannelList.value = !showChannelList.value
}

const selectMonitor = (monitorKey: string) => {
  selectedMonitor.value = monitorKey
  console.log(`切换到监控: ${monitorKey}`)
  showChannelList.value = false
}

const handleBoothSwitch = (boothType: string) => {
  if (boothType === 'exit') {
    router.push('/')
  }
}

// 搜索栏事件处理方法
const handleVehicleSearch = (data: { timeRange: [string, string] | null; keyword: string; isEmptyPlate: boolean }) => {
  console.log('车辆搜索:', data)
  // 这里可以添加搜索逻辑
  // 可以根据时间范围、关键词和空车牌选项进行搜索
}

const handleTicketEntry = () => {
  console.log('入场小票')
  // 这里可以添加入场小票逻辑
}

const handleUnrecognized = () => {
  console.log('无法识别')
  // 这里可以添加无法识别车辆处理逻辑
}

// 监控相关方法
const handleMonitorSelect = (monitor: MonitorData) => {
  selectedMonitorData.value = monitor
  console.log('选中监控:', monitor.id)
}

const handleMonitorDoubleClick = (monitor: MonitorData) => {
  console.log('双击监控:', monitor.id)
  selectedImageData.value = monitor
  showImageModal.value = true
}

// 现金收费成功处理
const handleCashPaymentSuccess = () => {
  // 显示成功提示
  message.success('操作收费成功，请收费车主15分钟内离开', {
    duration: 3000
  })

  // 取消图片选中状态
  selectedMonitorData.value = null
  selectedImageData.value = null

  // 清空 MonitorGrid 组件的选中状态
  if (monitorGridRef.value) {
    monitorGridRef.value.clearSelection()
  }
}

// 免费放行成功处理
const handleFreePassSuccess = (reason: string) => {
  // 显示成功提示
  message.success(`免费放行成功：${reason}`, {
    duration: 3000
  })

  // 取消图片选中状态
  selectedMonitorData.value = null
  selectedImageData.value = null

  // 清空 MonitorGrid 组件的选中状态
  if (monitorGridRef.value) {
    monitorGridRef.value.clearSelection()
  }
}

// 免费放行确认处理
const handleFreePassConfirm = (data: { type: string; reason: string }) => {
  const displayReason = data.type === 'leader' ? '领导同意免费' : data.type === 'internal' ? '内部车' : data.reason
  handleFreePassSuccess(displayReason)
  showFreePassModal.value = false
}

// 免费放行取消处理
const handleFreePassCancel = () => {
  showFreePassModal.value = false
}

// 处理免费按钮点击
const handleFreePassClick = () => {
  if (selectedMonitorData.value) {
    showFreePassModal.value = true
  }
}



// 生命周期
onMounted(() => {
  // 初始化数据
  refreshData()
})
</script>

<style scoped>
/* 使用与DashboardView相同的基础样式 */
@import '../styles/homepage-layout.css';

/* 中央岗亭特有样式 */
.center-content-area.central-booth {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: #f5f7fa;
}

/* 顶部搜索区域 */
.top-search-section {
  background: white;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

.search-left {
  flex: 1;
}

.page-title {
  color: #333;
  margin: 0 0 15px 0;
  font-size: 1.5em;
  font-weight: 600;
}

.search-filters {
  display: flex;
  align-items: center;
  gap: 15px;
}

.search-input {
  width: 300px;
}

.search-icon {
  color: #999;
}

.show-all-checkbox {
  white-space: nowrap;
}

.search-right {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 15px;
}

.search-right .action-buttons {
  display: flex;
  gap: 10px;
}

.vehicle-display {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 8px 15px;
  background: #f5f5f5;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.vehicle-icon {
  font-size: 16px;
}

.vehicle-number {
  font-weight: 600;
  color: #333;
}

.vehicle-action {
  color: #666;
  font-size: 14px;
}

/* 监控网格区域 */
.monitor-grid-section {
  flex: 1;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
}

/* MonitorGrid组件的样式已经在组件内部定义，这里不需要重复 */

/* 右侧控制面板 - 中央岗亭样式 */
.right-control-panel.central-panel {
  width: 420px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 20px;
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 免费放行按钮区域 */
.free-pass-section {
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
}

.free-pass-btn {
  width: 100%;
  height: 50px;
  font-size: 18px;
  font-weight: bold;
  background: linear-gradient(135deg, #ff6b35, #ff8c42);
  border: none;
  border-radius: 8px;
  color: white;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
}

.free-pass-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #ff5722, #ff7043);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 53, 0.4);
}

.free-pass-btn:disabled {
  background: #cccccc;
  color: #666666;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-container {
    flex-direction: column;
    align-items: stretch;
  }

  .search-right {
    align-self: flex-end;
  }
}

@media (max-width: 768px) {
  .center-content-area.central-booth {
    padding: 10px;
  }

  .search-filters {
    flex-direction: column;
    align-items: stretch;
  }

  .search-input {
    width: 100%;
  }
}
</style>
