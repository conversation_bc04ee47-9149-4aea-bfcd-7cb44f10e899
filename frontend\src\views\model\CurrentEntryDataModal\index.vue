<template>
  <div>
    <NModal
    :show="visible"
    preset="card"
    title="当前进次数据明细（不包含线上支付和其他地面道闸数据源）"
    size="huge"
    :mask-closable="true"
    :closable="true"
    style="width: 95vw; height: 90vh;"
    @update:show="handleUpdateVisible"
  >
    <!-- 搜索区域 -->
    <div class="search-section">
      <div class="search-row">
        <div class="search-item">
          <span class="search-label">车牌号码</span>
          <NInput
            v-model:value="searchFilters.plateNumber"
            size="small"
            style="width: 200px;"
            placeholder="请输入车牌号码"
            clearable
          />
        </div>

        <div class="search-item">
          <span class="search-label">时间</span>
          <NDatePicker
            v-model:value="searchFilters.dateRange"
            type="datetimerange"
            size="small"
            style="width: 300px;"
            format="yyyy-MM-dd HH:mm:ss"
            :shortcuts="dateShortcuts"
            clearable
          />
        </div>

        <div class="search-item">
          <span class="search-label">放行类型</span>
          <NSelect
            v-model:value="searchFilters.releaseType"
            size="small"
            style="width: 120px;"
            :options="releaseTypeOptions"
            clearable
          />
        </div>
        
        <div class="search-buttons">
          <NSpace>
            <NButton type="primary" size="small" @click="handleSearch">查询</NButton>
            <NButton type="default" size="small" @click="handleReset">重置</NButton>
          </NSpace>
        </div>
      </div>
    </div>

    <!-- 数据表格 -->
    <NDataTable
      :columns="columns"
      :data="entryDataRecords"
      :pagination="pagination"
      :loading="loading"
      size="small"
      striped
      :scroll-x="1200"
      class="entry-data-table"
      empty-description="暂无数据"
      loading-description="加载中..."
    />

    <!-- 底部统计信息 -->
    <div class="footer-stats">
      <span>总计 当前进次数据: {{ totalStats.currentEntry }}MOP 总进次数据: {{ totalStats.totalEntry }}MOP 总离场数据: {{ totalStats.totalExit }}MOP 总进出数据: {{ totalStats.totalInOut }}MOP</span>
    </div>

    <!-- 底部操作区域 -->
    <template #footer>
      <div class="modal-footer">
        <NSpace>
          <NButton @click="handleClose">关闭</NButton>
        </NSpace>
      </div>
    </template>
  </NModal>

    <!-- 详情弹框 -->
    <DetailModal
      :visible="showDetailModal"
      :record="selectedRecord"
      @update:visible="showDetailModal = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, h } from 'vue'
import {
  NModal,
  NInput,
  NButton,
  NSpace,
  NDataTable,
  NDatePicker,
  NSelect,
  type DataTableColumns
} from 'naive-ui'
import DetailModal from './DetailModal.vue'

// ==================== 接口定义 ====================
interface EntryDataRecord {
  id: number
  plateNumber: string
  totalAmount: string
  actualAmount: string
  freeAmount: string
  actualDeduction: string
  entryTime: string | null
  entryLocation: string
  exitLocation: string
  exitTime: string
  releaseType: string
  status: string
}

interface SearchFilters {
  plateNumber: string
  dateRange: [number, number] | null
  releaseType: string
}

interface TotalStats {
  currentEntry: string
  totalEntry: string
  totalExit: string
  totalInOut: string
}



// ==================== Props 和 Emits ====================
interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const loading = ref(false)

// 详情弹框相关
const showDetailModal = ref(false)
const selectedRecord = ref<EntryDataRecord | null>(null)

// 获取今天的日期范围
const getTodayRange = (): [number, number] => {
  const now = new Date()
  const start = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 0, 0, 0, 0)
  const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59, 999)
  return [start.getTime(), end.getTime()]
}

// 搜索筛选条件
const searchFilters = ref<SearchFilters>({
  plateNumber: '',
  dateRange: null,
  releaseType: '全部'
})

// 初始化时设置今天的日期
onMounted(() => {
  searchFilters.value.dateRange = getTodayRange()
  console.log('初始化时间范围:', searchFilters.value.dateRange)
})

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条`
})

// 统计数据
const totalStats = ref<TotalStats>({
  currentEntry: '436.1',
  totalEntry: '436.1',
  totalExit: '391.1',
  totalInOut: '391.1'
})

// ==================== 选项数据 ====================
const releaseTypeOptions = [
  { label: '全部', value: '全部' },
  { label: '正常离开', value: '正常离开' },
  { label: '异常离开', value: '异常离开' },
  { label: '免费放行', value: '免费放行' }
]

// 日期快捷选项
const dateShortcuts = {
  '今天': (): [number, number] => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    return [start.getTime(), end.getTime()]
  },
  '昨天': (): [number, number] => {
    const now = new Date()
    const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000)
    const start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
    const end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
    return [start.getTime(), end.getTime()]
  },
  '最近7天': (): [number, number] => {
    const now = new Date()
    const start = new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000)
    start.setHours(0, 0, 0, 0)
    const end = new Date()
    end.setHours(23, 59, 59, 999)
    return [start.getTime(), end.getTime()]
  }
}

// ==================== 模拟数据 ====================
const entryDataRecords = ref<EntryDataRecord[]>([
  {
    id: 1,
    plateNumber: 'TT7000',
    totalAmount: '15.5MOP',
    actualAmount: '15.5MOP',
    freeAmount: '0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 08:30:15',
    entryLocation: '电单车入口',
    exitLocation: '电单车出口',
    exitTime: '2025-01-15 17:45:02',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 2,
    plateNumber: 'TT5330',
    totalAmount: '25.0MOP',
    actualAmount: '20.0MOP',
    freeAmount: '5.0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 09:15:30',
    entryLocation: '私家车入口',
    exitLocation: '私家车出口',
    exitTime: '2025-01-15 14:16:05',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 3,
    plateNumber: 'C334',
    totalAmount: '169.1MOP',
    actualAmount: '169.1MOP',
    freeAmount: '0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-14 10:26:42',
    entryLocation: '私家车入口',
    exitLocation: '私家车出口',
    exitTime: '2025-01-15 17:17:52',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 4,
    plateNumber: 'MY1234',
    totalAmount: '8.0MOP',
    actualAmount: '0MOP',
    freeAmount: '8.0MOP',
    actualDeduction: '8.0MOP',
    entryTime: '2025-01-15 11:20:10',
    entryLocation: '电单车入口',
    exitLocation: '电单车出口',
    exitTime: '2025-01-15 15:53:11',
    releaseType: '免费放行',
    status: '详情'
  },
  {
    id: 5,
    plateNumber: 'AB1234',
    totalAmount: '45.5MOP',
    actualAmount: '45.5MOP',
    freeAmount: '0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 07:45:20',
    entryLocation: '私家车入口',
    exitLocation: '私家车出口',
    exitTime: '2025-01-15 18:30:45',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 6,
    plateNumber: 'CD5678',
    totalAmount: '12.0MOP',
    actualAmount: '12.0MOP',
    freeAmount: '0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 13:15:30',
    entryLocation: '电单车入口',
    exitLocation: '电单车出口',
    exitTime: '2025-01-15 16:20:15',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 7,
    plateNumber: 'EF9012',
    totalAmount: '30.0MOP',
    actualAmount: '25.0MOP',
    freeAmount: '5.0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 10:30:45',
    entryLocation: '私家车入口',
    exitLocation: '私家车出口',
    exitTime: '2025-01-15 19:45:30',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 8,
    plateNumber: '',
    totalAmount: '0MOP',
    actualAmount: '0MOP',
    freeAmount: '0MOP',
    actualDeduction: '0MOP',
    entryTime: null,
    entryLocation: '',
    exitLocation: '电单车出口',
    exitTime: '2025-01-15 12:30:20',
    releaseType: '异常离开',
    status: '详情'
  },
  {
    id: 9,
    plateNumber: 'GH3456',
    totalAmount: '18.5MOP',
    actualAmount: '18.5MOP',
    freeAmount: '0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 14:20:10',
    entryLocation: '电单车入口',
    exitLocation: '电单车出口',
    exitTime: '2025-01-15 20:15:45',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 10,
    plateNumber: 'IJ7890',
    totalAmount: '55.0MOP',
    actualAmount: '50.0MOP',
    freeAmount: '5.0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 06:30:00',
    entryLocation: '私家车入口',
    exitLocation: '私家车出口',
    exitTime: '2025-01-15 21:45:30',
    releaseType: '正常离开',
    status: '详情'
  },
  {
    id: 11,
    plateNumber: 'KL2468',
    totalAmount: '22.0MOP',
    actualAmount: '0MOP',
    freeAmount: '22.0MOP',
    actualDeduction: '22.0MOP',
    entryTime: '2025-01-15 15:45:20',
    entryLocation: '电单车入口',
    exitLocation: '电单车出口',
    exitTime: '2025-01-15 18:20:10',
    releaseType: '免费放行',
    status: '详情'
  },
  {
    id: 12,
    plateNumber: 'MN1357',
    totalAmount: '35.5MOP',
    actualAmount: '35.5MOP',
    freeAmount: '0MOP',
    actualDeduction: '0MOP',
    entryTime: '2025-01-15 09:00:15',
    entryLocation: '私家车入口',
    exitLocation: '私家车出口',
    exitTime: '2025-01-15 22:30:45',
    releaseType: '正常离开',
    status: '详情'
  }
])

// ==================== 表格列定义 ====================
const columns: DataTableColumns<EntryDataRecord> = [
  {
    title: '车牌号码',
    key: 'plateNumber',
    width: 100,
    align: 'center',
    fixed: 'left',
    render: (row: EntryDataRecord) => row.plateNumber || '--'
  },
  {
    title: '应收总额',
    key: 'totalAmount',
    width: 100,
    align: 'center'
  },
  {
    title: '实收金额',
    key: 'actualAmount',
    width: 100,
    align: 'center'
  },
  {
    title: '免费金额',
    key: 'freeAmount',
    width: 100,
    align: 'center'
  },
  {
    title: '实际抵扣',
    key: 'actualDeduction',
    width: 100,
    align: 'center'
  },
  {
    title: '入场时间',
    key: 'entryTime',
    width: 150,
    align: 'center',
    render: (row: EntryDataRecord) => row.entryTime || '--'
  },
  {
    title: '入口地点',
    key: 'entryLocation',
    width: 120,
    align: 'center',
    render: (row: EntryDataRecord) => row.entryLocation || '--'
  },
  {
    title: '出口地点',
    key: 'exitLocation',
    width: 120,
    align: 'center',
    render: (row: EntryDataRecord) => row.exitLocation || '--'
  },
  {
    title: '出场时间',
    key: 'exitTime',
    width: 150,
    align: 'center'
  },
  {
    title: '放行类型',
    key: 'releaseType',
    width: 100,
    align: 'center'
  },
  {
    title: '详情',
    key: 'status',
    width: 80,
    align: 'center',
    render(row: EntryDataRecord) {
      return h(
        NButton,
        {
          type: 'primary',
          size: 'small',
          text: true,
          onClick: () => handleViewDetail(row)
        },
        { default: () => '详情' }
      )
    }
  }
]



// ==================== 计算属性 ====================
/**
 * 根据筛选条件过滤数据记录
 */
const filteredRecords = computed(() => {
  let records = entryDataRecords.value

  // 车牌号筛选
  if (searchFilters.value.plateNumber) {
    records = records.filter(record =>
      record.plateNumber.toLowerCase().includes(searchFilters.value.plateNumber.toLowerCase())
    )
  }

  // 时间范围筛选
  if (searchFilters.value.dateRange) {
    const [startTime, endTime] = searchFilters.value.dateRange
    records = records.filter(record => {
      const exitTime = new Date(record.exitTime).getTime()
      const entryTime = record.entryTime ? new Date(record.entryTime).getTime() : exitTime
      return (exitTime >= startTime && exitTime <= endTime) ||
             (entryTime >= startTime && entryTime <= endTime)
    })
  }

  // 放行类型筛选
  if (searchFilters.value.releaseType && searchFilters.value.releaseType !== '全部') {
    records = records.filter(record =>
      record.releaseType === searchFilters.value.releaseType
    )
  }

  return records
})

// ==================== 方法定义 ====================
/**
 * 处理弹框显示状态更新
 */
const handleUpdateVisible = (visible: boolean) => {
  emit('update:visible', visible)
}

/**
 * 关闭弹框
 */
const handleClose = () => {
  emit('update:visible', false)
}

/**
 * 查看详情
 */
const handleViewDetail = (record: EntryDataRecord) => {
  selectedRecord.value = record
  showDetailModal.value = true
  console.log('查看详情:', record)
}



/**
 * 搜索数据
 */
const handleSearch = async () => {
  loading.value = true
  try {
    // 这里可以调用API获取数据
    console.log('搜索条件:', searchFilters.value)

    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 重置分页
    pagination.value.page = 1

    console.log('搜索完成')
  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 重置搜索条件
 */
const handleReset = () => {
  // 重置所有搜索条件
  searchFilters.value.plateNumber = ''
  searchFilters.value.dateRange = getTodayRange()
  searchFilters.value.releaseType = '全部'

  // 重置分页
  pagination.value.page = 1

  console.log('已重置搜索条件:', searchFilters.value)
}

/**
 * 监听弹框显示状态
 */
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 弹框打开时加载数据
    handleSearch()
  }
})
</script>

<style scoped>
.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 80px;
}

.search-buttons {
  margin-left: auto;
}

.entry-data-table {
  margin-bottom: 16px;
}

.footer-stats {
  padding: 12px 16px;
  background-color: #f5f5f5;
  border-radius: 4px;
  margin-bottom: 16px;
  font-size: 14px;
  color: #666;
  text-align: left;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
}

:deep(.n-data-table) {
  font-size: 12px;
}

:deep(.n-data-table th) {
  font-weight: 600;
  background-color: #fafafa;
}

:deep(.n-data-table td) {
  padding: 8px 12px;
}


</style>
