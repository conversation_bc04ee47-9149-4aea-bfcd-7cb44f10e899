<template>
  <div>
    <NModal
    :show="visible"
    preset="card"
    title="车辆出入明细"
    :mask-closable="false"
    style="width: 85vw; height: 85vh;"
    @update:show="handleUpdateVisible"
    @close="handleClose"
  >
    <!-- 搜索和筛选区域 -->
    <div class="search-section">
      <div class="search-row">
        <div class="search-item">
          <span class="search-label">车牌号码</span>
          <NInput
            v-model:value="searchFilters.plateNumber"
            placeholder="请输入车牌号码"
            size="small"
            style="width: 150px;"
            clearable
          />
        </div>
        
        <div class="search-item">
          <span class="search-label">时间范围</span>
          <NDatePicker
            v-model:value="searchFilters.dateRange"
            type="datetimerange"
            size="small"
            style="width: 300px;"
            format="yyyy-MM-dd HH:mm:ss"
            :shortcuts="dateShortcuts"
            placeholder="请选择时间范围"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            clearable
          />
        </div>
        
        <div class="search-item">
          <span class="search-label">车辆类型(进出)</span>
          <NSelect
            v-model:value="searchFilters.vehicleType"
            size="small"
            style="width: 120px;"
            :options="vehicleTypeOptions"
            placeholder="请选择"
            clearable
          />
        </div>

        <div class="search-item">
          <span class="search-label">应用场景类型</span>
          <NSelect
            v-model:value="searchFilters.channelType"
            size="small"
            style="width: 120px;"
            :options="channelTypeOptions"
            placeholder="请选择"
            clearable
          />
        </div>

        <div class="search-item">
          <span class="search-label">车辆类型</span>
          <NSelect
            v-model:value="searchFilters.vehicleStatus"
            size="small"
            style="width: 120px;"
            :options="vehicleStatusOptions"
            placeholder="请选择"
            clearable
          />
        </div>

        <div class="search-item">
          <span class="search-label">出行类型</span>
          <NSelect
            v-model:value="searchFilters.entryType"
            size="small"
            style="width: 120px;"
            :options="entryTypeOptions"
            placeholder="请选择"
            clearable
          />
        </div>

        <div class="search-item">
          <span class="search-label">出行类型状态</span>
          <NSelect
            v-model:value="searchFilters.entryStatus"
            size="small"
            style="width: 120px;"
            :options="entryStatusOptions"
            placeholder="请选择"
            clearable
          />
        </div>
        
        <div class="search-item">
          <NCheckbox v-model:checked="searchFilters.showAbnormal">
            异常车辆
          </NCheckbox>
        </div>

        <div class="search-item">
          <NCheckbox v-model:checked="searchFilters.showUnpaid">
            未缴费
          </NCheckbox>
        </div>
      </div>
      
      <div class="search-buttons">
        <NSpace>
          <NButton type="primary" size="small" @click="handleSearch">搜索</NButton>
          <NButton type="default" size="small" @click="handleReset">重置</NButton>
        </NSpace>
      </div>
    </div>

    <!-- 车辆记录表格 -->
    <NDataTable
      :columns="columns"
      :data="filteredRecords"
      :pagination="pagination"
      :loading="loading"
      size="small"
      striped
      :scroll-x="1400"
      class="vehicle-records-table"
      empty-description="暂无数据"
      loading-description="加载中..."
    />

    <!-- 底部操作区域 -->
    <template #footer>
      <div class="modal-footer">
        <NSpace>
          <NButton @click="handleClose">关闭</NButton>
        </NSpace>
      </div>
    </template>
  </NModal>

    <!-- 图片查看弹框 -->
    <ImageModal
      :visible="showImageModal"
      :record="selectedRecord"
      @update:visible="showImageModal = $event"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, h } from 'vue'
import {
  NModal,
  NInput,
  NButton,
  NSpace,
  NDataTable,
  NDatePicker,
  NSelect,
  NCheckbox,
  type DataTableColumns
} from 'naive-ui'
import ImageModal from './ImageModal.vue'

// ==================== 接口定义 ====================
interface VehicleRecord {
  id: number
  plateNumber: string
  entryTime: string
  exitTime?: string
  vehicleType: string
  channelName: string
  vehicleStatus: string
  entryType: string
  entryStatus: string
  remarks: string
  operator: string
  isAbnormal: boolean
  isPaid: boolean
}

interface SearchFilters {
  plateNumber: string
  dateRange: [number, number] | null
  vehicleType: string
  channelType: string
  vehicleStatus: string
  entryType: string
  entryStatus: string
  showAbnormal: boolean
  showUnpaid: boolean
}

// ==================== Props 和 Emits ====================
interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const loading = ref(false)

// 搜索筛选条件
const searchFilters = ref<SearchFilters>({
  plateNumber: '',
  dateRange: null,
  vehicleType: '全部',
  channelType: '全部',
  vehicleStatus: '全部',
  entryType: '全部',
  entryStatus: '全部',
  showAbnormal: false,
  showUnpaid: false
})

// 分页配置
const pagination = ref({
  page: 1,
  pageSize: 20,
  showSizePicker: true,
  pageSizes: [10, 20, 50, 100],
  showQuickJumper: true,
  prefix: ({ itemCount }: { itemCount: number }) => `共 ${itemCount} 条记录`,
  suffix: ({ page, pageCount }: { page: number, pageCount: number }) => `第 ${page} 页，共 ${pageCount} 页`
})

// ==================== 选项数据 ====================
const vehicleTypeOptions = [
  { label: '全部', value: '全部' },
  { label: '进入', value: '进入' },
  { label: '离开', value: '离开' }
]

const channelTypeOptions = [
  { label: '全部', value: '全部' },
  { label: '电单车入口', value: '电单车入口' },
  { label: '电单车出口', value: '电单车出口' },
  { label: '小型车入口', value: '小型车入口' },
  { label: '小型车出口', value: '小型车出口' }
]

const vehicleStatusOptions = [
  { label: '全部', value: '全部' },
  { label: '电单车', value: '电单车' },
  { label: '小型车', value: '小型车' }
]

const entryTypeOptions = [
  { label: '全部', value: '全部' },
  { label: '正常', value: '正常' },
  { label: '异常', value: '异常' }
]

const entryStatusOptions = [
  { label: '全部', value: '全部' },
  { label: '正常出行', value: '正常出行' },
  { label: '中途离场', value: '中途离场' },
  { label: '开通道车', value: '开通道车' },
  { label: '升降道车', value: '升降道车' }
]

// 日期快捷选项
const dateShortcuts = {
  '今天': (): [number, number] => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    return [start.getTime(), end.getTime()]
  },
  '昨天': (): [number, number] => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1)
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 1, 23, 59, 59)
    return [start.getTime(), end.getTime()]
  },
  '最近7天': (): [number, number] => {
    const now = new Date()
    const start = new Date(now.getFullYear(), now.getMonth(), now.getDate() - 6)
    const end = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59)
    return [start.getTime(), end.getTime()]
  }
}

// ==================== 响应式数据 ====================
/**
 * 图片查看弹框显示状态
 */
const showImageModal = ref(false)

/**
 * 当前选中的记录
 */
const selectedRecord = ref<VehicleRecord | null>(null)



// ==================== 模拟数据 ====================
const vehicleRecords = ref<VehicleRecord[]>([
  {
    id: 1,
    plateNumber: 'TT7000',
    entryTime: '2025-07-22 17:46:08',
    exitTime: undefined,
    vehicleType: '电单车',
    channelName: '电单车入口',
    vehicleStatus: '电单车',
    entryType: '正常',
    entryStatus: '正常出行',
    remarks: '四川省人民政府办公厅关于印发四川省条例 条文',
    operator: 'admin',
    isAbnormal: false,
    isPaid: false
  },
  {
    id: 2,
    plateNumber: 'TT7000',
    entryTime: '2025-07-22 17:46:10',
    exitTime: undefined,
    vehicleType: '电单车',
    channelName: '电单车入口',
    vehicleStatus: '电单车',
    entryType: '正常',
    entryStatus: '正常出行',
    remarks: '四川省人民政府办公厅关于印发四川省条例 条文',
    operator: 'admin',
    isAbnormal: false,
    isPaid: false
  },
  {
    id: 3,
    plateNumber: '',
    entryTime: '2025-07-22 17:46:02',
    exitTime: undefined,
    vehicleType: '电单车',
    channelName: '电单车出口',
    vehicleStatus: '电单车',
    entryType: '正常',
    entryStatus: '正常出行',
    remarks: '出口门禁设备离线 (11)',
    operator: 'admin',
    isAbnormal: false,
    isPaid: false
  },
  {
    id: 4,
    plateNumber: 'C334',
    entryTime: '2025-07-22 15:56:59',
    exitTime: undefined,
    vehicleType: '电单车',
    channelName: '电单车入口',
    vehicleStatus: '电单车',
    entryType: '正常',
    entryStatus: '正常出行',
    remarks: '打卡成功',
    operator: 'admin',
    isAbnormal: false,
    isPaid: false
  },
  {
    id: 5,
    plateNumber: 'MY1234',
    entryTime: '2025-07-22 15:53:11',
    exitTime: undefined,
    vehicleType: '电单车',
    channelName: '电单车入口',
    vehicleStatus: '电单车',
    entryType: '正常',
    entryStatus: '正常出行',
    remarks: '打卡成功',
    operator: 'admin',
    isAbnormal: false,
    isPaid: false
  }
  // 更多模拟数据...
])

// ==================== 表格列定义 ====================
const columns: DataTableColumns<VehicleRecord> = [
  {
    title: '车牌号码',
    key: 'plateNumber',
    width: 100,
    align: 'center',
    fixed: 'left'
  },
  {
    title: '出入时间',
    key: 'entryTime',
    width: 150,
    align: 'center'
  },
  {
    title: '出入地点',
    key: 'channelName',
    width: 120,
    align: 'center'
  },
  {
    title: '车辆类型',
    key: 'vehicleType',
    width: 100,
    align: 'center'
  },
  {
    title: '车辆状态',
    key: 'vehicleStatus',
    width: 100,
    align: 'center'
  },
  {
    title: '出行类型',
    key: 'entryType',
    width: 100,
    align: 'center'
  },
  {
    title: '备注',
    key: 'remarks',
    width: 200,
    align: 'left',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '操作人',
    key: 'operator',
    width: 80,
    align: 'center'
  },
  {
    title: '查看图片',
    key: 'actions',
    width: 100,
    align: 'center',
    fixed: 'right',
    render(row: VehicleRecord) {
      return h(
        NButton,
        {
          type: 'primary',
          size: 'small',
          text: true,
          onClick: () => handleViewImage(row)
        },
        { default: () => '查看图片' }
      )
    }
  }
]

// ==================== 计算属性 ====================
/**
 * 根据筛选条件过滤车辆记录
 * 支持多种筛选条件的组合使用
 */
const filteredRecords = computed(() => {
  let records = vehicleRecords.value

  // 车牌号码筛选
  if (searchFilters.value.plateNumber.trim()) {
    records = records.filter(record =>
      record.plateNumber.toLowerCase().includes(searchFilters.value.plateNumber.toLowerCase())
    )
  }

  // 时间范围筛选
  if (searchFilters.value.dateRange) {
    const [startTime, endTime] = searchFilters.value.dateRange
    records = records.filter(record => {
      const recordTime = new Date(record.entryTime).getTime()
      return recordTime >= startTime && recordTime <= endTime
    })
  }

  // 车辆类型筛选
  if (searchFilters.value.vehicleType && searchFilters.value.vehicleType !== '全部') {
    records = records.filter(record =>
      record.vehicleType === searchFilters.value.vehicleType
    )
  }

  // 通道类型筛选
  if (searchFilters.value.channelType && searchFilters.value.channelType !== '全部') {
    records = records.filter(record =>
      record.channelName === searchFilters.value.channelType
    )
  }

  // 车辆状态筛选
  if (searchFilters.value.vehicleStatus && searchFilters.value.vehicleStatus !== '全部') {
    records = records.filter(record =>
      record.vehicleStatus === searchFilters.value.vehicleStatus
    )
  }

  // 出入类型筛选
  if (searchFilters.value.entryType && searchFilters.value.entryType !== '全部') {
    records = records.filter(record =>
      record.entryType === searchFilters.value.entryType
    )
  }

  // 出入状态筛选
  if (searchFilters.value.entryStatus && searchFilters.value.entryStatus !== '全部') {
    records = records.filter(record =>
      record.entryStatus === searchFilters.value.entryStatus
    )
  }

  // 异常车辆筛选
  if (searchFilters.value.showAbnormal) {
    records = records.filter(record => record.isAbnormal)
  }

  // 未缴费筛选
  if (searchFilters.value.showUnpaid) {
    records = records.filter(record => !record.isPaid)
  }

  return records
})

// ==================== 方法定义 ====================
/**
 * 处理搜索功能
 * 执行搜索并重置分页到第一页
 */
const handleSearch = async () => {
  loading.value = true
  try {
    console.log('执行搜索，筛选条件:', searchFilters.value)
    console.log('搜索结果数量:', filteredRecords.value.length)

    // 重置分页到第一页
    pagination.value.page = 1

    // 这里可以调用API获取数据
    // const result = await vehicleRecordApi.search(searchFilters.value)
    // vehicleRecords.value = result.data

  } catch (error) {
    console.error('搜索失败:', error)
  } finally {
    loading.value = false
  }
}

/**
 * 重置搜索条件
 * 清空所有筛选条件并重新加载数据
 */
const handleReset = () => {
  // 重置所有搜索条件
  searchFilters.value.plateNumber = ''
  searchFilters.value.dateRange = null
  searchFilters.value.vehicleType = '全部'
  searchFilters.value.channelType = '全部'
  searchFilters.value.vehicleStatus = '全部'
  searchFilters.value.entryType = '全部'
  searchFilters.value.entryStatus = '全部'
  searchFilters.value.showAbnormal = false
  searchFilters.value.showUnpaid = false

  // 重置分页
  pagination.value.page = 1

  console.log('已重置搜索条件')

  // 重新执行搜索以更新显示
  handleSearch()
}

/**
 * 查看车辆图片
 * 打开图片查看弹框
 */
const handleViewImage = (record: VehicleRecord) => {
  console.log('查看车辆图片:', record)
  // 打开图片查看弹框
  showImageModal.value = true
  selectedRecord.value = record
}

/**
 * 更新显示状态
 */
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

/**
 * 关闭弹框
 */
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// ==================== 监听器 ====================
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      // 弹框打开时加载数据
      handleSearch()
    }
  }
)

// 组件名称通过文件名自动推断，无需export default
</script>

<style scoped>
.search-section {
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.search-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  min-width: 60px;
}

.search-buttons {
  display: flex;
  justify-content: flex-end;
}

.vehicle-records-table {
  margin-top: 16px;
  height: calc(85vh - 300px);
  overflow: auto;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
}

/* 确保弹框内容能正确显示 */
:deep(.n-modal) {
  max-height: 85vh;
  overflow: hidden;
}

:deep(.n-card__content) {
  height: calc(85vh - 120px);
  overflow: auto;
  padding: 20px;
}

:deep(.n-data-table) {
  font-size: 12px;
}

:deep(.n-data-table th) {
  font-weight: 600;
  background-color: #fafafa;
}



.image-item :deep(.n-image) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
