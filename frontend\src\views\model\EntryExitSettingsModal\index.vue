<template>
  <NModal
    :show="visible"
    @update:show="$emit('update:visible', $event)"
    preset="card"
    title="进出口设置"
    :style="{ width: '90%', maxWidth: '1200px' }"
    :mask-closable="false"
  >
    <!-- 标签页和操作区域 -->
    <div class="settings-header">
      <div class="tab-section">
        <NButtonGroup>
          <NButton
            v-for="tab in tabs"
            :key="tab.key"
            @click="activeTab = tab.key"
            :type="activeTab === tab.key ? 'primary' : 'default'"
            size="medium"
          >
            {{ tab.label }}
          </NButton>
        </NButtonGroup>
      </div>

      <div class="action-section">
        <NSpace>
          <NInput
            v-model:value="searchKeyword"
            placeholder="请输入通道名称"
            size="small"
            style="width: 200px;"
            clearable
          />
          <NButton type="primary" size="small" @click="handleSearch">搜索</NButton>
          <NButton type="default" size="small" @click="handleBatchLongLift">一键长抬</NButton>
          <NButton type="default" size="small" @click="handleBatchNormal">一键常规</NButton>
        </NSpace>
      </div>
    </div>

    <!-- 通道列表表格 -->
    <NDataTable
      :columns="columns"
      :data="filteredChannels"
      :pagination="false"
      :bordered="true"
      size="medium"
      class="channels-table"
    />

    <template #footer>
      <NSpace justify="end">
        <NButton @click="handleCancel">取消</NButton>
      </NSpace>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'
import {
  NModal,
  NDataTable,
  NButton,
  NButtonGroup,
  NSpace,
  NTag,
  NInput,
  type DataTableColumns
} from 'naive-ui'

interface Channel {
  id: number
  name: string
  status: string
  statusClass: string
  barrierControl: 'long' | 'normal'  // 长抬/常规
  cameraControl: 'enabled' | 'disabled'  // 启用/禁用
  type: 'entry' | 'exit'
}

interface Tab {
  key: string
  label: string
}

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
  (e: 'save', channels: Channel[]): void
}

defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const activeTab = ref('all')
const searchKeyword = ref('')

// 标签页配置
const tabs: Tab[] = [
  { key: 'all', label: '全部' },
  { key: 'entry', label: '入口地点' },
  { key: 'exit', label: '出口地点' }
]

// 通道数据 - 根据图片内容
const channels = ref<Channel[]>([
  {
    id: 1,
    name: '私家车入口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'entry'
  },
  {
    id: 2,
    name: '私家车出口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'exit'
  },
  {
    id: 3,
    name: '电单车入口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'entry'
  },
  {
    id: 4,
    name: '电单车出口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'normal',
    cameraControl: 'disabled',
    type: 'exit'
  },
  {
    id: 5,
    name: 'B2入口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'entry'
  },
  {
    id: 6,
    name: 'B2出口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'exit'
  },
  {
    id: 7,
    name: 'B2电单车入口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'entry'
  },
  {
    id: 8,
    name: 'B2电单车出口',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'exit'
  },
  {
    id: 9,
    name: '私家车入口2',
    status: '已停到位',
    statusClass: 'normal',
    barrierControl: 'long',
    cameraControl: 'enabled',
    type: 'entry'
  }
])

// 表格列定义
const columns: DataTableColumns<Channel> = [
  {
    title: '通道名称',
    key: 'name',
    width: 200,
    align: 'left'
  },
  {
    title: '道闸状态',
    key: 'status',
    width: 120,
    align: 'center',
    render(row: Channel) {
      return h(NTag, { type: 'success', size: 'small' }, { default: () => row.status })
    }
  },
  {
    title: '挡车器控制',
    key: 'barrierControl',
    width: 200,
    align: 'center',
    render(row: Channel) {
      return h(NButtonGroup, { size: 'small' }, {
        default: () => [
          h(NButton, {
            type: row.barrierControl === 'long' ? 'primary' : 'default',
            size: 'small',
            onClick: () => updateBarrierControl(row, 'long')
          }, { default: () => '长抬' }),
          h(NButton, {
            type: row.barrierControl === 'normal' ? 'primary' : 'default',
            size: 'small',
            onClick: () => updateBarrierControl(row, 'normal')
          }, { default: () => '常规' })
        ]
      })
    }
  },
  {
    title: '相机控制',
    key: 'cameraControl',
    width: 200,
    align: 'center',
    render(row: Channel) {
      return h(NButtonGroup, { size: 'small' }, {
        default: () => [
          h(NButton, {
            type: row.cameraControl === 'disabled' ? 'primary' : 'default',
            size: 'small',
            onClick: () => updateCameraControl(row, 'disabled')
          }, { default: () => '禁用' }),
          h(NButton, {
            type: row.cameraControl === 'enabled' ? 'primary' : 'default',
            size: 'small',
            onClick: () => updateCameraControl(row, 'enabled')
          }, { default: () => '启用' })
        ]
      })
    }
  }
]

// 计算过滤后的通道列表
const filteredChannels = computed(() => {
  let filtered = channels.value

  // 根据标签页过滤
  if (activeTab.value === 'entry') {
    filtered = filtered.filter(channel => channel.type === 'entry')
  } else if (activeTab.value === 'exit') {
    filtered = filtered.filter(channel => channel.type === 'exit')
  }

  // 根据搜索关键词过滤
  if (searchKeyword.value.trim()) {
    const keyword = searchKeyword.value.toLowerCase().trim()
    filtered = filtered.filter(channel =>
      channel.name.toLowerCase().includes(keyword)
    )
  }

  return filtered
})

// 更新挡车器控制 - 立即请求后台
const updateBarrierControl = async (channel: Channel, control: 'long' | 'normal') => {
  channel.barrierControl = control
  // 立即向后台发送请求
  try {
    // TODO: 调用API更新挡车器控制
    console.log(`更新通道 ${channel.name} 挡车器控制为: ${control}`)
  } catch (error) {
    console.error('更新挡车器控制失败:', error)
  }
}

// 更新相机控制 - 立即请求后台
const updateCameraControl = async (channel: Channel, control: 'enabled' | 'disabled') => {
  channel.cameraControl = control
  // 立即向后台发送请求
  try {
    // TODO: 调用API更新相机控制
    console.log(`更新通道 ${channel.name} 相机控制为: ${control}`)
  } catch (error) {
    console.error('更新相机控制失败:', error)
  }
}

// ==================== 筛选和搜索功能 ====================
/**
 * 处理搜索功能
 * 当用户点击搜索按钮时触发，可以添加额外的搜索逻辑
 * 搜索逻辑主要通过 computed 属性 filteredChannels 实现
 */
const handleSearch = () => {
  console.log('搜索关键词:', searchKeyword.value)
  console.log('筛选结果数量:', filteredChannels.value.length)

  // 这里可以添加额外的搜索逻辑，比如：
  // - 搜索统计
  // - 搜索历史记录
  // - 搜索结果高亮
  // - 发送搜索事件给父组件
}

/**
 * 一键长抬功能
 * 将当前筛选显示的所有通道的挡车器控制设置为长抬模式
 * 只对当前搜索结果中的通道生效
 */
const handleBatchLongLift = async () => {
  try {
    const visibleChannels = filteredChannels.value
    console.log(`开始执行一键长抬，影响通道数量: ${visibleChannels.length}`)

    for (const channel of visibleChannels) {
      await updateBarrierControl(channel, 'long')
    }

    console.log('一键长抬操作完成')
    // 可以添加成功提示
    // message.success(`成功设置 ${visibleChannels.length} 个通道为长抬模式`)
  } catch (error) {
    console.error('一键长抬操作失败:', error)
    // 可以添加错误提示
    // message.error('一键长抬操作失败，请重试')
  }
}

/**
 * 一键常规功能
 * 将当前筛选显示的所有通道的挡车器控制设置为常规模式
 * 只对当前搜索结果中的通道生效
 */
const handleBatchNormal = async () => {
  try {
    const visibleChannels = filteredChannels.value
    console.log(`开始执行一键常规，影响通道数量: ${visibleChannels.length}`)

    for (const channel of visibleChannels) {
      await updateBarrierControl(channel, 'normal')
    }

    console.log('一键常规操作完成')
    // 可以添加成功提示
    // message.success(`成功设置 ${visibleChannels.length} 个通道为常规模式`)
  } catch (error) {
    console.error('一键常规操作失败:', error)
    // 可以添加错误提示
    // message.error('一键常规操作失败，请重试')
  }
}

// 处理取消
const handleCancel = () => {
  emit('close')
}
</script>

<style scoped>
.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
  flex-wrap: wrap;
}



.channels-table {
  margin-top: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-header {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}
</style>
