<template>
  <NModal
    :show="visible"
    @update:show="handleUpdateVisible"
    preset="card"
    :style="{ width: '95vw', maxWidth: '1200px', height: '85vh' }"
    title="监控大图"
    size="huge"
    :bordered="false"
    :segmented="{ content: 'soft', footer: 'soft' }"
  >
    <div class="large-image-container">
      <img
        v-if="imageData"
        :src="imageData.image"
        :alt="imageData.plateNumber || '无车牌'"
        class="large-image"
      />
      <div v-else class="no-image">
        <span>暂无图片</span>
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
import { NModal } from 'naive-ui'

defineOptions({
  name: 'ImageModal'
})

// 定义监控数据类型
interface MonitorData {
  id: string
  plateNumber: string
  timestamp: string
  status: string
  image: string
  selected?: boolean
}

// Props
interface Props {
  visible: boolean
  imageData?: MonitorData | null
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  imageData: null
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
}

const emit = defineEmits<Emits>()

// 方法
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}
</script>

<style scoped>
/* 大图弹框样式 */
.large-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 60vh;
  min-height: 400px;
}

.large-image {
  width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 8px;
}

.no-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  color: #999;
  font-size: 18px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .large-image-container {
    height: 50vh;
    min-height: 300px;
  }
}
</style>
