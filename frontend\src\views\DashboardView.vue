<template>
  <div class="parking-management">
    <!-- Unified Top Bar -->
  <TopMenuBar
      status-label="剩余车位"
      :status-value="`${availableParkingSpaces}个`"
      :is-refreshing="isRefreshing"
      :current-booth="'exit'"
      @refresh="refreshData"
      @go-back="showSettings"
      @toggleChannelList="toggleChannelList"
      @switchBooth="handleBoothSwitch"
    />

    <!-- Main Content Area -->
    <div class="main-content-area">
      <!-- 左侧浮动入口列表面板 -->
      <NDrawer v-model:show="showChannelList" :width="300" placement="left">
        <NDrawerContent title="入口列表" closable>
          <NList>
            <NListItem
              v-for="channel in channelList"
              :key="channel.key"
              :class="{ 'n-list-item--active': selectedChannel === channel.key }"
              @click="selectChannel(channel.key)"
              style="cursor: pointer;"
            >
              <template #prefix>
                <span style="font-size: 18px; margin-right: 8px;">{{ channel.icon }}</span>
              </template>
              {{ channel.label }}
            </NListItem>
          </NList>
        </NDrawerContent>
      </NDrawer>

      <!-- Center Content Area -->
      <div class="center-content-area">
        <!-- 上方双视频区域 -->
        <div class="video-top-section">
          <div class="video-window">
            <div class="video-header">
              <span class="video-title">入口监控</span>
              <span class="video-time">{{ currentTime }}</span>
            </div>
            <div class="video-content">
              <SimpleCameraDisplay
                camera-id="entrance"
                :camera-ip="entranceCameraIp"
                :camera-port="entranceCameraWsPort"
                channel-name="入口摄像头"
                :auto-connect="false"
                :token="authStore.token || ''"
              />
            </div>
            <div class="video-footer">
              <span class="video-status">{{ entranceCameraStatus }}</span>
            </div>
          </div>

          <div class="video-window">
            <div class="video-header">
              <span class="video-title">出口监控</span>
              <span class="video-time">{{ currentTime }}</span>
            </div>
            <div class="video-content">
              <SimpleCameraDisplay
                camera-id="exit"
                :camera-ip="exitCameraIp"
                :camera-port="exitCameraWsPort"
                channel-name="出口摄像头"
                :auto-connect="false"
                :token="authStore.token || ''"
              />
            </div>
            <div class="video-footer">
              <span class="video-status">{{ exitCameraStatus }}</span>
            </div>
          </div>
        </div>

        <!-- 下方三视频区域 -->
        <div class="video-bottom-section">
          <div class="video-controls">
            <span class="controls-title">入场推荐匹配(选择相似车辆，进行入场匹配)</span>
            <div class="controls-buttons">
              <div class="button-group">
                <button class="btn-small">◀</button>
                <button class="btn-small">▶</button>
              </div>
            </div>
          </div>

          <div class="video-grid">
            <div class="video-window small">
              <div class="video-content">
                <div class="video-placeholder">
                  <div class="camera-icon">📹</div>
                </div>
              </div>
            </div>
            <div class="video-window small">
              <div class="video-content">
                <div class="video-placeholder">
                  <div class="camera-icon">📹</div>
                </div>
              </div>
            </div>
            <div class="video-window small">
              <div class="video-content">
                <div class="video-placeholder">
                  <div class="camera-icon">📹</div>
                </div>
              </div>
            </div>
          </div>

          <div class="bottom-status">
            <span class="status-text">出场不成功，请处理</span>
            <button class="btn-warning btn-small">手动处理</button>
          </div>
        </div>
      </div>

      <!-- Right Control Panel -->
      <ExitControlPanel
        :selected-monitor-data="selectedMonitorData"
        @normal-exit="handleNormalExit"
        @manual-exit="handleManualExit"
        @exception-exit="handleExceptionExit"
        @view-details="handleViewDetails"
        @open-gate="handleOpenGate"
        @refresh-data="handleRefreshData"
        @billing-details="handleBillingDetailsModal"
      />
    </div>
  </div>

    <!-- 车牌前缀选择组件 -->
    <SimplePlatePrefixSelector
      :visible="showPrefixModal"
      :model-value="currentPlatePrefix"
      @update:visible="showPrefixModal = $event"
      @update:model-value="currentPlatePrefix = $event"
      @confirm="handlePrefixConfirm"
    />

    <EntryExitSettingsModal
      :visible="showEntryExitSettingsModal"
      @update:visible="showEntryExitSettingsModal = $event"
    />

    <VehicleRecordModal
      :visible="showVehicleRecordModal"
      @update:visible="showVehicleRecordModal = $event"
      @close="handleVehicleRecordClose"
    />

    <CurrentEntryDataModal
      :visible="showCurrentEntryDataModal"
      @update:visible="showCurrentEntryDataModal = $event"
    />

    <FreePassModal
      :visible="showFreePassModal"
      @update:visible="showFreePassModal = $event"
      @confirm="handleFreePassConfirm"
    />

    <BatchPlateEditModal
      :visible="showBatchPlateEditModal"
      @update:visible="showBatchPlateEditModal = $event"
      @batchEdit="handleBatchPlateEdit"
    />

    <ShiftHandoverModal
      :visible="showShiftHandoverModal"
      @update:visible="showShiftHandoverModal = $event"
      @confirm="handleShiftHandoverConfirm"
    />

    <BillingDetailsModal
      :visible="showBillingDetailsModal"
      @update:visible="showBillingDetailsModal = $event"
    />
</template>

<script setup lang="ts">
// ==================== 导入模块 ====================
import { ref, computed, onMounted, onUnmounted, shallowRef } from 'vue'
import { useRouter } from 'vue-router'
import { useMessage } from 'naive-ui'

// 组件导入
import TopMenuBar from '../components/TopMenuBar.vue'
import ExitControlPanel from '../components/ExitControlPanel/index.vue'
import SimplePlatePrefixSelector from '../components/SimplePlatePrefixSelector.vue'
import SimpleCameraDisplay from '../components/SimpleCameraDisplay.vue'
import EntryExitSettingsModal from './model/EntryExitSettingsModal/index.vue'
import VehicleRecordModal from './model/VehicleRecordModal/index.vue'
import CurrentEntryDataModal from './model/CurrentEntryDataModal/index.vue'
import FreePassModal from './model/FreePassModal/index.vue'
import BatchPlateEditModal from './model/BatchPlateEditModal/index.vue'
import ShiftHandoverModal from './model/ShiftHandoverModal/index.vue'
import BillingDetailsModal from './model/BillingDetailsModal/index.vue'

// 服务和工具导入
import { ParkingService } from '../services/parkingService'
import { CameraType, CameraStatus } from '../services/cameraManager'
import { useAuthStore } from '../stores/auth'
// import { ElectronTester } from '../utils/electronTest'

// ==================== 初始化 ====================
const router = useRouter()
const parkingService = new ParkingService()
const authStore = useAuthStore()
const message = useMessage()

// ==================== 响应式数据 ====================

// 界面状态
const currentTime = ref('')
const selectedVehicle = ref<string | null>(null)
const selectedMonitorData = ref<any | null>(null)
const isRefreshing = ref(false)
const selectedChannel = ref('private-exit')
const showChannelList = ref(false)
const showPrefixModal = ref(false)
const showEntryExitSettingsModal = ref(false)
const showVehicleRecordModal = ref(false)

// 车牌信息
const currentPlatePrefix = ref('空')
const currentPlateNumber = ref('MY1234')
const currentTicketNumber = ref('T202401001')
const currentVehicleType = ref('小型车')
const currentPlateType = ref('小型车')

// 停车位数据
const availableParkingSpaces = ref(156)

// 摄像头配置
const entranceCameraIp = ref('*********')
const exitCameraIp = ref('**********')
const entranceCameraWsPort = ref(9080)
const exitCameraWsPort = ref(9999)
const entranceCameraType = ref(CameraType.HUAXIA)
const exitCameraType = ref(CameraType.HUAXIA)
const entranceCameraStatus = ref('通道1 | 在线')
const exitCameraStatus = ref('通道2 | 离线')

// Electron 相关
const isElectronApp = ref(false)

// 弹框状态
const showCurrentEntryDataModal = ref(false)
const showFreePassModal = ref(false)
const showBatchPlateEditModal = ref(false)
const showShiftHandoverModal = ref(false)
const showBillingDetailsModal = ref(false)

// 通道列表数据
const channelList = [
  { key: 'private-entrance', label: '私家车入口', icon: '🚗' },
  { key: 'private-exit', label: '私家车出口', icon: '🚗' },
  { key: 'electric-entrance', label: '电单车入口', icon: '🛵' },
  { key: 'electric-exit', label: '电单车出口', icon: '🛵' },
  { key: 'b2-entrance', label: 'B2入口', icon: '🚪' },
  { key: 'b2-exit', label: 'B2出口', icon: '🚪' },
  { key: 'b2-electric-entrance', label: 'B2电单车入口', icon: '🛵' },
  { key: 'b2-electric-exit', label: 'B2电单车出口', icon: '🛵' },
  { key: 'private-entrance-2', label: '私家车入口2', icon: '🚗' }
]

// 硬件状态数据 (使用shallowRef优化性能)
const hardwareStatus = shallowRef({
  printer: true,
  camera: true,
  cardReader: true,
  groundSensor: false
})

// 今日统计数据
const todayStats = shallowRef({
  totalVehicles: 127,
  currentOccupancy: 45,
  totalRevenue: 1250.50,
  averageStayTime: 85
})

// 当前车辆列表
const currentVehicles = ref([
  {
    id: '1',
    licensePlate: '京A12345',
    vehicleType: 'car',
    entryTime: new Date(Date.now() - 3600000), // 1小时前
    stayDuration: 60,
    amount: 15.50,
    paymentStatus: 'unpaid',
    hasIssue: false
  },
  {
    id: '2',
    licensePlate: '京B67890',
    vehicleType: 'suv',
    entryTime: new Date(Date.now() - 7200000), // 2小时前
    stayDuration: 120,
    amount: 25.00,
    paymentStatus: 'paid',
    hasIssue: false
  },
  {
    id: '3',
    licensePlate: '京C11111',
    vehicleType: 'truck',
    entryTime: new Date(Date.now() - 14400000), // 4小时前
    stayDuration: 240,
    amount: 45.00,
    paymentStatus: 'unpaid',
    hasIssue: true
  }
])

// ==================== 计算属性 ====================
const selectedVehicleData = computed(() => {
  if (!selectedVehicle.value) return null
  return currentVehicles.value.find(vehicle => vehicle.id === selectedVehicle.value)
})

const hardwareStatusSummary = computed(() => {
  const status = hardwareStatus.value
  const total = Object.keys(status).length
  const working = Object.values(status).filter(Boolean).length
  return {
    total,
    working,
    percentage: Math.round((working / total) * 100)
  }
})

const formattedRevenue = computed(() => {
  return `¥${todayStats.value.totalRevenue.toFixed(2)}`
})

// ==================== 工具方法 ====================
// 时间更新优化
let timeCache = ''
let lastSecond = -1

const updateTime = () => {
  const now = new Date()
  const currentSecond = now.getSeconds()

  if (currentSecond !== lastSecond) {
    timeCache = now.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    lastSecond = currentSecond
    currentTime.value = timeCache
  }
}

const formatTime = (date: Date) => {
  return date.toLocaleString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatDuration = (minutes: number) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  if (hours > 0) {
    return `${hours}小时${mins}分钟`
  }
  return `${mins}分钟`
}

const getVehicleTypeText = (type: string) => {
  const types: Record<string, string> = {
    car: '小型车',
    suv: 'SUV',
    truck: '货车',
    motorcycle: '摩托车'
  }
  return types[type] || '未知'
}

const getPaymentStatusText = (status: string) => {
  const statuses: Record<string, string> = {
    paid: '已付费',
    unpaid: '未付费',
    processing: '处理中'
  }
  return statuses[status] || '未知'
}

// ==================== 通道管理方法 ====================
const toggleChannelList = () => {
  showChannelList.value = !showChannelList.value
}

// 岗亭切换处理
const handleBoothSwitch = (boothType: string) => {
  if (boothType === 'central') {
    router.push('/central')
  }
}

const selectChannel = (channel: string) => {
  selectedChannel.value = channel
  showChannelList.value = false
  console.log('选择通道:', channel)
  updatePlateInfoByChannel(channel)
}

const updatePlateInfoByChannel = (channel: string) => {
  const channelData: Record<string, any> = {
    'private-exit': { prefix: '粤', number: 'B12345', ticket: 'T202401001', type: '小型车' },
    'private-entrance': { prefix: '京', number: 'A88888', ticket: 'T202401002', type: '小型车' },
    'electric-exit': { prefix: '电', number: 'E12345', ticket: 'E202401001', type: '电单车' },
    'b2-exit': { prefix: '沪', number: 'C99999', ticket: 'B202401001', type: '小型车' }
  }

  const data = channelData[channel] || { prefix: '空', number: 'MY1234', ticket: 'T202401001', type: '小型车' }
  currentPlatePrefix.value = data.prefix
  currentPlateNumber.value = data.number
  currentTicketNumber.value = data.ticket
  currentVehicleType.value = data.type
}

// ==================== 车牌管理方法 ====================
const confirmPlateModify = () => {
  console.log('确认修改车牌:', currentPlatePrefix.value + currentPlateNumber.value)
}

const handlePrefixConfirm = (prefix: string) => {
  console.log('确认车牌前缀:', prefix)
}

// ==================== 进出口设置弹框方法 ====================
const handleEntryExitSettingsModal = () => {
  showEntryExitSettingsModal.value = !showEntryExitSettingsModal.value
}

const handleVehicleRecordModal = () => {
  showVehicleRecordModal.value = !showVehicleRecordModal.value
}

const handleVehicleRecordClose = () => {
  showVehicleRecordModal.value = false
  console.log('车辆出入明细弹框已关闭')
}

const handleCurrentEntryDataModal = () => {
  showCurrentEntryDataModal.value = !showCurrentEntryDataModal.value
}

const handleCurrentEntryDataClose = () => {
  showCurrentEntryDataModal.value = false
  console.log('当前进次数据明细弹框已关闭')
}

const handleFreePassModal = () => {
  showFreePassModal.value = true
}

const handleFreePassConfirm = (data: { type: string; reason: string }) => {
  console.log('免费放行确认:', data)
  // 这里可以调用实际的免费放行API
  // await freePassAPI(data)

  // 显示成功消息
  console.log(`免费放行成功 - 类型: ${data.type === 'leader' ? '领导同意免费' : '内部车'}, 原因: ${data.reason}`)
}

const handleBatchPlateEditModal = () => {
  showBatchPlateEditModal.value = true
}

const handleShiftHandoverModal = () => {
  showShiftHandoverModal.value = true
}

const handleShiftHandoverConfirm = (data: any) => {
  console.log('交班确认数据:', data)
  // 这里可以调用实际的交班API
  // 可能需要刷新页面数据或跳转到登录页
}

const handleBillingDetailsModal = () => {
  showBillingDetailsModal.value = true
}

const handleBatchPlateEdit = (data: { plateIds: number[]; newPlateNumber: string }) => {
  console.log('批量修改车牌:', data)
  // 这里可以调用实际的批量修改车牌API
  // await batchEditPlateAPI(data)

  // 显示成功消息
  console.log(`批量修改车牌成功 - 修改了 ${data.plateIds.length} 个车牌`)
}

// ==================== 车辆管理方法 ====================
const selectVehicle = (vehicleId: string) => {
  selectedVehicle.value = vehicleId
}

// ==================== 数据刷新方法 ====================
const refreshData = async () => {
  isRefreshing.value = true
  try {
    console.log('刷新数据')
    const stats = await parkingService.getTodayStats()
    todayStats.value = stats
  } catch (error) {
    console.error('刷新数据失败:', error)
  } finally {
    isRefreshing.value = false
  }
}

// ==================== 导航方法 ====================
const showSettings = () => {
  router.push('/settings')
}

const manualEntry = () => {
  router.push('/parking')
}

const processPayment = () => {
  router.push('/payment')
}

const viewReports = () => {
  router.push('/reports')
}

// ==================== 硬件管理方法 ====================
const checkHardware = () => {
  console.log('检测硬件')
  hardwareStatus.value.printer = Math.random() > 0.2
  hardwareStatus.value.camera = Math.random() > 0.1
  hardwareStatus.value.cardReader = Math.random() > 0.15
  hardwareStatus.value.groundSensor = Math.random() > 0.3
}

const emergencyOpen = () => {
  console.log('紧急开闸')
  alert('紧急开闸已执行')
}

// ==================== ExitControlPanel 相关方法 ====================
const handleNormalExit = () => {
  // 显示成功提示
  message.success('车辆正常出场，闸门已开启', {
    duration: 3000
  })

  // 取消选中状态
  setTimeout(() => {
    selectedMonitorData.value = null
  }, 2000)
}

const handleManualExit = () => {
  if (selectedMonitorData.value) {
    message.warning('手动出场处理中，请确认车辆已离开', {
      duration: 3000
    })
    // 可以打开手动处理弹框
    // showManualExitModal.value = true
  }
}

const handleExceptionExit = () => {
  if (selectedMonitorData.value) {
    message.error('异常出场处理，请联系管理员', {
      duration: 3000
    })
    // 可以打开异常处理弹框
    // showExceptionModal.value = true
  }
}

const handleViewDetails = () => {
  if (selectedMonitorData.value) {
    // 显示车辆详细信息
    showBillingDetailsModal.value = true
  }
}

const handleOpenGate = () => {
  message.info('手动开闸执行中...', {
    duration: 2000
  })
  // 执行开闸操作
  console.log('手动开闸')
}

const handleRefreshData = () => {
  message.info('正在刷新数据...', {
    duration: 1000
  })
  // 刷新监控数据
  refreshData()
}

// ==================== Electron 相关 (临时注释) ====================
// const testElectronFeatures = async () => {
//   console.log('🚀 开始测试 Electron 桌面功能...')
//   await ElectronTester.runFullTest()
// }

// 摄像头状态处理 (临时注释)
/*
const handleEntranceCameraStatus = (status: CameraStatus) => {
  console.log('🎥 入口摄像头状态变化:', status)

  switch (status) {
    case CameraStatus.CONNECTED:
      entranceCameraStatus.value = '入口通道1 | 在线'
      message.success('入口摄像头连接成功')
      break
    case CameraStatus.CONNECTING:
      entranceCameraStatus.value = '入口通道1 | 连接中'
      break
    case CameraStatus.ERROR:
      entranceCameraStatus.value = '入口通道1 | 错误'
      break
    default:
      entranceCameraStatus.value = '入口通道1 | 离线'
      break
  }
}
*/

/*
const handleExitCameraStatus = (status: CameraStatus) => {
  console.log('🎥 出口摄像头状态变化:', status)

  switch (status) {
    case CameraStatus.CONNECTED:
      exitCameraStatus.value = '出口通道1 | 在线'
      message.success('出口摄像头连接成功')
      break
    case CameraStatus.CONNECTING:
      exitCameraStatus.value = '出口通道1 | 连接中'
      break
    case CameraStatus.ERROR:
      exitCameraStatus.value = '出口通道1 | 错误'
      break
    default:
      exitCameraStatus.value = '出口通道1 | 离线'
      break
  }
}

const handleCameraError = (error: string) => {
  console.error('🎥 摄像头错误:', error)
  // 移除频繁的错误弹窗，只保留控制台日志
}

// 初始化摄像头配置
const initCameraConfig = async () => {
  try {
    const params = await CameraApi.getCarParams()
    const cameraType = CameraApi.parseCameraType(params.CAM_TYPE)

    // 更新摄像头类型
    entranceCameraType.value = cameraType
    exitCameraType.value = cameraType

    console.log('🎥 摄像头配置初始化完成:', {
      type: CameraApi.getCameraTypeName(cameraType),
      username: params.CAM_USER,
      password: params.CAM_PWD
    })
  } catch (error) {
    console.error('🎥 初始化摄像头配置失败:', error)
    message.warning('获取摄像头配置失败，使用默认配置')
  }
}
*/



// ==================== 生命周期管理 ====================
let timeInterval: ReturnType<typeof setInterval> | null = null

onMounted(async () => {
  console.log('🎯 DashboardView 挂载完成')
  console.log('🎥 摄像头配置:', {
    entrance: {
      ip: entranceCameraIp.value,
      port: entranceCameraWsPort.value,
      type: entranceCameraType.value
    },
    exit: {
      ip: exitCameraIp.value,
      port: exitCameraWsPort.value,
      type: exitCameraType.value
    }
  })

  // 初始化摄像头配置 (临时注释)
  // await initCameraConfig()

  // 启动时间更新定时器
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }
})
</script>

<style scoped>
@import '../styles/homepage-layout.css';

/* 自定义样式 */
.system-overview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
}

.overview-icon {
  font-size: 4em;
  margin-bottom: 20px;
}

.overview-title {
  font-size: 2em;
  font-weight: bold;
  color: #333;
  margin-bottom: 10px;
}

.overview-subtitle {
  font-size: 1.2em;
  color: #666;
  margin-bottom: 30px;
}

.system-stats {
  display: flex;
  gap: 40px;
}

.system-stat {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 1.8em;
  font-weight: bold;
  color: #1976d2;
}

.stat-text {
  font-size: 0.9em;
  color: #666;
  margin-top: 5px;
}

.dashboard-overview {
  padding: 20px;
}

.overview-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.vehicle-type-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8em;
  font-weight: 600;
}

.vehicle-type-badge.car {
  background: #e3f2fd;
  color: #1976d2;
}

.vehicle-type-badge.suv {
  background: #f3e5f5;
  color: #7b1fa2;
}

.vehicle-type-badge.truck {
  background: #fff3e0;
  color: #f57c00;
}

.overview-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.stat-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  text-align: center;
}

.stat-card .stat-label {
  font-size: 0.9em;
  color: #666;
  margin-bottom: 8px;
}

.stat-card .stat-value {
  font-size: 1.4em;
  font-weight: bold;
  color: #333;
}

.stat-card .stat-value.amount {
  color: #4caf50;
}

.stat-card .stat-value.paid {
  color: #4caf50;
}

.stat-card .stat-value.unpaid {
  color: #f44336;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 6px;
  background: #f8f9fa;
}

.device-item.online {
  border-left: 4px solid #4caf50;
}

.device-item.offline {
  border-left: 4px solid #f44336;
}

.device-icon {
  margin-right: 8px;
  font-size: 1.2em;
}

.device-name {
  flex: 1;
  font-weight: 500;
}

.device-status {
  font-size: 0.9em;
  font-weight: 600;
}

.device-item.online .device-status {
  color: #4caf50;
}

.device-item.offline .device-status {
  color: #f44336;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.action-btn {
  padding: 10px 15px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.action-btn.primary {
  background: #1976d2;
  color: white;
}

.action-btn.primary:hover {
  background: #1565c0;
}

.action-btn.secondary {
  background: #f5f5f5;
  color: #333;
}

.action-btn.secondary:hover {
  background: #e0e0e0;
}

.action-btn.warning {
  background: #ff9800;
  color: white;
}

.action-btn.warning:hover {
  background: #f57c00;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
}

.stat-item {
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.stat-item .stat-value {
  font-size: 1.2em;
  font-weight: bold;
  color: #1976d2;
}

.stat-item .stat-label {
  font-size: 0.8em;
  color: #666;
  margin-top: 4px;
}

/* Electron 测试按钮样式 */
.electron-test-btn {
  padding: 8px 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-left: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.electron-test-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.electron-test-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 摄像头占位符样式 */
.camera-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
  color: white;
  text-align: center;
  border-radius: 8px;
}

.camera-status {
  font-size: 24px;
  margin-bottom: 10px;
}

.camera-info {
  font-size: 14px;
  color: #888;
}

/* 简单按钮样式 */
.btn-small, .btn-default, .btn-primary, .btn-success, .btn-info, .btn-warning, .btn-error {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-default {
  background: #f0f0f0;
  color: #333;
}

.btn-primary {
  background: #1976d2;
  color: white;
}

.btn-success {
  background: #4caf50;
  color: white;
}

.btn-info {
  background: #2196f3;
  color: white;
}

.btn-warning {
  background: #ff9800;
  color: white;
}

.btn-error {
  background: #f44336;
  color: white;
}

.btn-block {
  width: 100%;
  margin-bottom: 8px;
}

.button-group {
  display: flex;
  gap: 8px;
}

.button-group-vertical {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.btn-default:hover {
  background: #e0e0e0;
}

.btn-primary:hover {
  background: #1565c0;
}

.btn-success:hover {
  background: #388e3c;
}

.btn-info:hover {
  background: #1976d2;
}

.btn-warning:hover {
  background: #f57c00;
}

.btn-error:hover {
  background: #d32f2f;
}

</style>
