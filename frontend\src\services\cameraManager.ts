// 摄像头类型枚举
export enum CameraType {
  ZHENSHI = 0,    // 臻识相机
  HUAXIA = 1,     // 华夏相机
  QIANYI = 2,     // 芊熠相机
  DAHUA = 3,      // 大华相机
  SHIJIE = 4,     // 视捷相机
  ZHENSHI_V2 = 5, // 臻识相机V2
  XINLUTONG = 6   // 信路通相机
}

// 摄像头状态枚举
export enum CameraStatus {
  DISCONNECTED = 'disconnected',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  ERROR = 'error'
}

// 摄像头配置接口
export interface CameraConfig {
  id: string
  ip: string
  port: number
  type: CameraType
  username?: string
  password?: string
  channelName?: string
}

// 简化的摄像头管理器
export class CameraManager {
  private cameras = new Map<string, CameraConfig>()
  private connections = new Map<string, WebSocket>()
  private statusCallbacks = new Map<string, (status: CameraStatus) => void>()

  // 注册摄像头
  registerCamera(config: CameraConfig) {
    this.cameras.set(config.id, config)
  }

  // 连接摄像头
  async connect(cameraId: string): Promise<boolean> {
    const config = this.cameras.get(cameraId)
    if (!config) {
      console.error('摄像头配置不存在:', cameraId)
      return false
    }

    try {
      this.updateStatus(cameraId, CameraStatus.CONNECTING)

      // 根据摄像头类型创建连接
      if (config.type === CameraType.HUAXIA) {
        return await this.connectHuaxiaCamera(config)
      }

      // 其他类型暂时返回false
      this.updateStatus(cameraId, CameraStatus.ERROR)
      return false
    } catch (error) {
      console.error('摄像头连接失败:', error)
      this.updateStatus(cameraId, CameraStatus.ERROR)
      return false
    }
  }

  // 华夏相机连接
  private async connectHuaxiaCamera(config: CameraConfig): Promise<boolean> {
    const wsUrl = `ws://${config.ip}:${config.port}/`
    const ws = new WebSocket(wsUrl)

    return new Promise((resolve) => {
      ws.onopen = () => {
        console.log('华夏相机连接成功:', config.id)
        this.connections.set(config.id, ws)
        this.updateStatus(config.id, CameraStatus.CONNECTED)
        resolve(true)
      }

      ws.onerror = () => {
        console.error('华夏相机连接失败:', config.id)
        this.updateStatus(config.id, CameraStatus.ERROR)
        resolve(false)
      }

      ws.onclose = () => {
        console.log('华夏相机连接关闭:', config.id)
        this.connections.delete(config.id)
        this.updateStatus(config.id, CameraStatus.DISCONNECTED)
      }
    })
  }

  // 断开连接
  disconnect(cameraId: string) {
    const ws = this.connections.get(cameraId)
    if (ws) {
      ws.close()
      this.connections.delete(cameraId)
    }
    this.updateStatus(cameraId, CameraStatus.DISCONNECTED)
  }

  // 设置状态回调
  onStatusChange(cameraId: string, callback: (status: CameraStatus) => void) {
    this.statusCallbacks.set(cameraId, callback)
  }

  // 更新状态
  private updateStatus(cameraId: string, status: CameraStatus) {
    const callback = this.statusCallbacks.get(cameraId)
    if (callback) {
      callback(status)
    }
  }

  // 获取连接状态
  getStatus(cameraId: string): CameraStatus {
    const ws = this.connections.get(cameraId)
    if (!ws) return CameraStatus.DISCONNECTED

    switch (ws.readyState) {
      case WebSocket.CONNECTING:
        return CameraStatus.CONNECTING
      case WebSocket.OPEN:
        return CameraStatus.CONNECTED
      default:
        return CameraStatus.DISCONNECTED
    }
  }
}

// 全局摄像头管理器实例
export const cameraManager = new CameraManager()