<template>
  <NModal
    :show="visible"
    preset="card"
    title="计费详情"
    :mask-closable="false"
    :closable="true"
    style="width: 900px; max-height: 80vh;"
    @update:show="handleUpdateVisible"
    @close="handleClose"
  >
    <!-- 计费详情表格 -->
    <div class="billing-table-container">
      <table class="billing-table">
        <thead>
          <tr>
            <th>计费规则</th>
            <th>计费时段</th>
            <th>停车区域</th>
            <th>时长</th>
            <th>金额</th>
            <th>计费备注</th>
            <th>时段详情</th>
          </tr>
        </thead>
        <tbody>
          <!-- 时租车-电单车 主行 -->
          <tr class="main-row">
            <td class="rule-cell">
              <div class="rule-name">时租车-电单车</div>
            </td>
            <td>
              <div class="time-item">
                <span class="time-icon">○</span>
                <span>2025-07-21 17:48:52</span>
              </div>
              <div class="time-item">
                <span class="time-icon">○</span>
                <span>2025-07-22 17:48:52</span>
              </div>
            </td>
            <td>私家车区域</td>
            <td>1天</td>
            <td>3.90MOP</td>
            <td>--</td>
            <td>
              <a href="#" @click.prevent="openDetailModal" class="detail-link">
                详情
              </a>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 详细计费时段弹框 -->
    <NModal
      :show="showDetailModal"
      preset="card"
      title="计费详情"
      :mask-closable="false"
      :closable="true"
      style="width: 800px; max-height: 80vh;"
      @update:show="showDetailModal = $event"
      @close="closeDetailModal"
    >
      <div class="detail-table-container">
        <table class="detail-table">
          <thead>
            <tr>
              <th>计费时段</th>
              <th>计费单价</th>
              <th>计费时长</th>
              <th>时段费用</th>
              <th>备注</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(detail, index) in billingDetails" :key="index" class="detail-row">
              <td>
                <div class="time-item">
                  <span class="time-icon">○</span>
                  <span>{{ detail.startTime }}</span>
                </div>
                <div class="time-item">
                  <span class="time-icon">○</span>
                  <span>{{ detail.endTime }}</span>
                </div>
              </td>
              <td>{{ detail.unitPrice }}</td>
              <td>{{ detail.duration }}</td>
              <td>{{ detail.amount }}</td>
              <td>{{ detail.remark }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </NModal>
  </NModal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { NModal } from 'naive-ui'

defineOptions({
  name: 'BillingDetailsModal'
})

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
}

const emit = defineEmits<Emits>()

// 状态
const showDetailModal = ref(false) // 详情弹框显示状态

// 计费详情数据
const billingDetails = ref([
  {
    startTime: '2025-07-21 17:48:52',
    endTime: '2025-07-22 17:48:52',
    unitPrice: '3分/0.00MOP',
    duration: '1天',
    amount: '3.90MOP',
    remark: '免费3分钟'
  },
  {
    startTime: '2025-07-21 17:48:52',
    endTime: '2025-07-22 17:48:52',
    unitPrice: '1小时/0.10MOP',
    duration: '1天',
    amount: '3.90MOP',
    remark: ''
  },
  {
    startTime: '2025-07-21 17:48:52',
    endTime: '2025-07-22 17:48:52',
    unitPrice: '1小时/0.10MOP',
    duration: '1天',
    amount: '3.90MOP',
    remark: '已过到期期时限'
  },
  {
    startTime: '2025-07-21 17:48:52',
    endTime: '2025-07-22 17:48:52',
    unitPrice: '1小时/0.40MOP',
    duration: '1天',
    amount: '3.90MOP',
    remark: '已过到期期时限'
  },
  {
    startTime: '2025-07-21 17:48:52',
    endTime: '2025-07-22 17:48:52',
    unitPrice: '1小时/0.10MOP',
    duration: '1天',
    amount: '3.90MOP',
    remark: ''
  }
])

// 方法
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
}

const openDetailModal = () => {
  showDetailModal.value = true
  console.log('打开详细计费时段弹框')
}

const closeDetailModal = () => {
  showDetailModal.value = false
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    console.log('获取计费详情数据')
  } else {
    // 主弹框关闭时，也关闭详情弹框
    showDetailModal.value = false
  }
})
</script>

<style scoped>
.billing-table-container {
  max-height: 60vh;
  overflow-y: auto;
}

.billing-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
}

.billing-table th {
  background: #f5f5f5;
  padding: 12px 8px;
  text-align: center;
  border: 1px solid #e0e0e0;
  font-weight: 500;
  color: #333;
}

.billing-table td {
  padding: 8px;
  border: 1px solid #e0e0e0;
  text-align: center;
  vertical-align: middle;
}

.main-row {
  background: #fafafa;
}

.rule-cell {
  background: #f8f9fa;
  vertical-align: middle;
  text-align: center;
}

.rule-name {
  font-weight: 500;
  color: #333;
}

.time-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 2px;
  font-size: 12px;
}

.time-item:last-child {
  margin-bottom: 0;
}

.time-icon {
  margin-right: 6px;
  color: #999;
  font-size: 10px;
}

.detail-link {
  color: #1890ff;
  text-decoration: none;
  font-size: 12px;
}

.detail-link:hover {
  text-decoration: underline;
}

.detail-header {
  background: #f0f0f0;
  font-weight: 500;
}

.detail-header .detail-label {
  text-align: center;
  color: #666;
}

.detail-header .detail-value {
  text-align: center;
  color: #666;
}

.detail-row {
  background: #fafafa;
}

.detail-row td {
  font-size: 12px;
  color: #666;
}

/* 详情表格样式 */
.detail-table-container {
  max-height: 60vh;
  overflow-y: auto;
}

.detail-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 13px;
  background: white;
}

.detail-table th {
  background-color: #f5f5f5;
  border: 1px solid #e0e0e0;
  padding: 12px 8px;
  text-align: center;
  font-weight: 500;
  color: #333;
}

.detail-table td {
  border: 1px solid #e0e0e0;
  padding: 8px;
  text-align: center;
  vertical-align: middle;
}

.detail-table .detail-row {
  background-color: #fafafa;
}

.detail-table .detail-row:hover {
  background-color: #f0f0f0;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .billing-table {
    font-size: 12px;
  }

  .billing-table th,
  .billing-table td {
    padding: 6px 4px;
  }

  .detail-table {
    font-size: 12px;
  }

  .detail-table th,
  .detail-table td {
    padding: 6px 4px;
  }
}
</style>
