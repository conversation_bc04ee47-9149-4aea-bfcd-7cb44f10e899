<template>
  <div class="top-menu-bar-container">
    <div class="unified-top-bar">
    <!-- 左侧区域 -->
    <div class="top-left-section">
      <!-- 入口列表按钮 -->
      <div class="menu-container">
        <NButton
          @click="toggleChannelList"
          :type="showChannelList ? 'primary' : 'default'"
          size="medium"
          class="entrance-list-btn"
          :class="{ 'active': showChannelList }"
          round
        >
          <template #icon>
            <span class="menu-icon">🚪</span>
          </template>
          入口列表
        </NButton>
      </div>

      <div v-if="pageTitle && pageIcon" class="page-indicator">
        <span class="page-icon">{{ pageIcon }}</span>
        <span class="page-title">{{ pageTitle }}</span>
      </div>
    </div>



    <!-- 右侧区域 -->
    <div class="top-right-section">
      <div class="status-info">
        <!-- 岗亭切换按钮 -->
        <div class="booth-switch-container">
          <div class="booth-switch-tabs">
            <div
              @click="switchToBooth('exit')"
              :class="['booth-tab', { 'active': props.currentBooth === 'exit' }]"
            >
              出口岗亭
            </div>
            <div
              @click="switchToBooth('central')"
              :class="['booth-tab', { 'active': props.currentBooth === 'central' }]"
            >
              中央岗亭
            </div>
          </div>
        </div>

        <div class="time-display-right">
          <div class="current-time-only">{{ currentTime }}</div>
        </div>

        <div class="status-item" @click="openParkingModal">
          <span class="status-label">{{ statusLabel }}:</span>
          <span class="status-value">{{ statusValue }}</span>
        </div>

        <div class="action-buttons">
          <NSpace>
            <NButton
              @click="refreshData"
              type="info"
              :loading="isRefreshing"
              size="medium"
            >
              <template #icon>
                <span class="btn-icon">🔄</span>
              </template>
              {{ isRefreshing ? '刷新中...' : '刷新' }}
            </NButton>

          </NSpace>
        </div>

        <!-- 用户信息 -->
        <div class="user-container">
          <NDropdown
            :options="userMenuOptions"
            @select="handleUserMenuSelect"
            trigger="click"
            placement="bottom-end"
          >
            <div class="user-info">
              <div class="user-avatar">
                <span class="avatar-icon">👤</span>
              </div>
              <div class="user-details">
                <span class="username">收费员</span>
              </div>
              <span class="dropdown-arrow">▼</span>
            </div>
          </NDropdown>



        </div>
      </div>
    </div>
  </div>



  <!-- 修改剩余车位弹窗 -->
  <div v-if="showParkingModal" class="parking-modal-overlay" @click="closeParkingModal">
    <div class="parking-modal" @click.stop>
      <div class="parking-modal-header">
        <h3 class="parking-title">修改剩余车位</h3>
        <button class="close-btn" @click="closeParkingModal">✕</button>
      </div>

      <div class="parking-modal-body">
        <div class="parking-table">
          <div class="table-header">
            <div class="header-cell">区域名称</div>
            <div class="header-cell">总车位数</div>
            <div class="header-cell">剩余车位数</div>
            <div class="header-cell">操作</div>
          </div>

          <div class="table-row" v-for="area in parkingAreas" :key="area.id">
            <div class="table-cell area-name">{{ area.name }}</div>
            <div class="table-cell total-spaces">{{ area.totalSpaces }}</div>
            <div class="table-cell remaining-spaces">
              <input
                type="number"
                v-model="area.remainingSpaces"
                :max="area.totalSpaces"
                min="0"
                class="spaces-input"
              />
            </div>
            <div class="table-cell action-cell">
              <button class="modify-btn" @click="modifyAreaSpaces(area)">修改</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 交班确认弹窗 -->
  <ShiftHandoverModal
    :visible="showShiftHandoverModal"
    @update:visible="showShiftHandoverModal = $event"
    @confirm="handleShiftHandoverConfirm"
  />

  <!-- 进出口设置弹窗 (临时注释) -->
  <!-- <EntrySettingsModal
    :visible="showEntrySettingsModal"
    @update:visible="showEntrySettingsModal = $event"
    @close="closeEntrySettingsModal"
    @save="handleEntrySettingsSave"
  /> -->

  <!-- 监控设置弹窗 -->
  <div v-if="showMonitorSettingsModal" class="monitor-settings-modal-overlay" @click="closeMonitorSettingsModal">
    <div class="monitor-settings-modal" @click.stop>
      <div class="monitor-settings-header">
        <h3 class="monitor-settings-title">监控设置</h3>
        <button class="close-btn" @click="closeMonitorSettingsModal">✕</button>
      </div>

      <div class="monitor-settings-body">
        <!-- 提示信息 -->
        <div class="monitor-notice">
          用於配置當前需要顯示的監控視頻數量及其顯示定位通道 (windows server 系統不支援獨立監控)
        </div>

        <!-- 监控模式选择 -->
        <div class="monitor-modes">
          <button
            v-for="mode in monitorSettingsData.modes"
            :key="mode.value"
            @click="selectMonitorMode(mode.value)"
            class="mode-btn"
            :class="{ active: monitorSettingsData.selectedMode === mode.value }"
          >
            {{ mode.label }}
          </button>

          <!-- 独立窗口模式选项 -->
          <div class="independent-window-option">
            <label class="checkbox-container">
              <input
                type="checkbox"
                v-model="monitorSettingsData.independentWindow"
                @change="toggleIndependentWindow"
                class="checkbox-input"
              />
              <span class="checkbox-label">獨立窗口模式</span>
            </label>
          </div>
        </div>

        <!-- 监控视频显示区域 -->
        <div class="monitor-display-area">
          <!-- 这里可以显示监控视频预览 -->
          <div class="monitor-placeholder">
            <span class="placeholder-text">监控视频显示区域</span>
            <span class="placeholder-mode">当前模式: {{ monitorSettingsData.selectedMode }}</span>
          </div>
        </div>

        <!-- 底部按钮 -->
        <div class="monitor-settings-footer">
          <button class="monitor-btn cancel" @click="cancelMonitorSettings">
            取消
          </button>
          <button class="monitor-btn confirm" @click="confirmMonitorSettings">
            确定
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 收费明细弹窗 -->
  <CurrentEntryDataModal
    :visible="showChargeDetailsModal"
    @update:visible="showChargeDetailsModal = $event"
  />



  <!-- 车牌修改弹框 -->
  <BatchPlateEditModal
    :visible="showPlateModificationModal"
    @update:visible="showPlateModificationModal = $event"
    @batch-edit="handleBatchPlateEdit"
  />



  <!-- 车辆进入明细弹窗 -->
  <div v-if="showVehicleEntryModal" class="vehicle-entry-modal-overlay" @click="closeVehicleEntryModal">
    <div class="vehicle-entry-modal" @click.stop>
      <div class="vehicle-entry-header">
        <h3 class="vehicle-entry-title">车辆进入明细</h3>
        <button class="close-btn" @click="closeVehicleEntryModal">✕</button>
      </div>

      <div class="vehicle-entry-body">
        <!-- 搜索筛选区域 -->
        <div class="entry-filter-section">
          <div class="filter-row">
            <div class="filter-group">
              <label>开始时间:</label>
              <input
                type="date"
                v-model="vehicleEntryData.searchFilters.startDate"
                class="filter-input date-input"
              />
              <input
                type="time"
                v-model="vehicleEntryData.searchFilters.startTime"
                class="filter-input time-input"
              />
            </div>

            <div class="filter-separator">-</div>

            <div class="filter-group">
              <label>结束时间:</label>
              <input
                type="date"
                v-model="vehicleEntryData.searchFilters.endDate"
                class="filter-input date-input"
              />
              <input
                type="time"
                v-model="vehicleEntryData.searchFilters.endTime"
                class="filter-input time-input"
              />
            </div>
          </div>

          <div class="filter-row">
            <div class="filter-group">
              <label>车牌号码:</label>
              <input
                type="text"
                v-model="vehicleEntryData.searchFilters.plateNumber"
                placeholder="请输入车牌号码"
                class="filter-input"
              />
            </div>

            <div class="filter-group">
              <label>进入方式:</label>
              <select
                v-model="vehicleEntryData.searchFilters.entryMethod"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.entryMethodOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>车辆类型:</label>
              <select
                v-model="vehicleEntryData.searchFilters.vehicleType"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.vehicleTypeOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>付款状态:</label>
              <select
                v-model="vehicleEntryData.searchFilters.paymentStatus"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.paymentStatusOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>

            <div class="filter-group">
              <label>进入通道:</label>
              <select
                v-model="vehicleEntryData.searchFilters.entryChannel"
                class="filter-select"
              >
                <option
                  v-for="option in vehicleEntryData.entryChannelOptions"
                  :key="option"
                  :value="option"
                >
                  {{ option }}
                </option>
              </select>
            </div>
          </div>

          <div class="filter-actions">
            <div class="action-checkboxes">
              <label class="checkbox-label">
                <input type="checkbox" class="checkbox-input" />
                <span>只显示异常</span>
              </label>
              <label class="checkbox-label">
                <input type="checkbox" class="checkbox-input" />
                <span>打印报表</span>
              </label>
            </div>

            <div class="action-buttons">
              <button class="filter-btn search" @click="searchVehicleEntry">
                搜索
              </button>
              <button class="filter-btn export" @click="exportVehicleEntry">
                导出
              </button>
            </div>
          </div>
        </div>

        <!-- 车辆进入明细表格 -->
        <div class="vehicle-entry-table">
          <div class="table-header">
            <div class="header-cell plate-number">车牌号码</div>
            <div class="header-cell entry-time">进入时间</div>
            <div class="header-cell entry-method">进入方式</div>
            <div class="header-cell vehicle-type">车辆类型</div>
            <div class="header-cell payment-status">付款状态</div>
            <div class="header-cell entry-channel">进入通道</div>
            <div class="header-cell operator">操作员</div>
            <div class="header-cell remarks">备注</div>
            <div class="header-cell actions">操作</div>
          </div>

          <div class="table-body">
            <div
              v-for="record in vehicleEntryData.records"
              :key="record.id"
              class="table-row"
            >
              <div class="table-cell plate-number">
                {{ record.plateNumber }}
              </div>
              <div class="table-cell entry-time">{{ record.entryTime }}</div>
              <div class="table-cell entry-method">{{ record.entryMethod }}</div>
              <div class="table-cell vehicle-type">{{ record.vehicleType }}</div>
              <div class="table-cell payment-status">
                <span class="status-badge" :class="record.paymentStatus">
                  {{ record.paymentStatus }}
                </span>
              </div>
              <div class="table-cell entry-channel">{{ record.entryChannel }}</div>
              <div class="table-cell operator">{{ record.operator }}</div>
              <div class="table-cell remarks">{{ record.remarks }}</div>
              <div class="table-cell actions">
                <button
                  class="action-link"
                  @click="editVehicleEntry(record.id)"
                >
                  {{ record.actions }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 密码修改弹窗 -->
  <div v-if="showPasswordChangeModal" class="password-change-modal-overlay" @click="closePasswordChangeModal">
    <div class="password-change-modal" @click.stop>
      <div class="password-change-header">
        <h3 class="password-change-title">修改密码</h3>
        <button class="close-btn" @click="closePasswordChangeModal">✕</button>
      </div>

      <div class="password-change-body">
        <div class="password-form">
          <!-- 当前密码 -->
          <div class="form-group">
            <label class="form-label">当前密码</label>
            <div class="password-input-wrapper">
              <input
                :type="passwordChangeData.showCurrentPassword ? 'text' : 'password'"
                v-model="passwordChangeData.currentPassword"
                placeholder="请输入当前密码"
                class="password-input"
                autocomplete="current-password"
              />
              <button
                type="button"
                class="password-toggle"
                @click="togglePasswordVisibility('current')"
              >
                {{ passwordChangeData.showCurrentPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>
          </div>

          <!-- 新密码 -->
          <div class="form-group">
            <label class="form-label">新密码</label>
            <div class="password-input-wrapper">
              <input
                :type="passwordChangeData.showNewPassword ? 'text' : 'password'"
                v-model="passwordChangeData.newPassword"
                @input="checkPasswordStrength(passwordChangeData.newPassword)"
                placeholder="请输入新密码"
                class="password-input"
                autocomplete="new-password"
              />
              <button
                type="button"
                class="password-toggle"
                @click="togglePasswordVisibility('new')"
              >
                {{ passwordChangeData.showNewPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>

            <!-- 密码强度指示器 -->
            <div class="password-strength">
              <div class="strength-bar">
                <div
                  class="strength-fill"
                  :class="{
                    'weak': passwordChangeData.passwordStrength <= 2,
                    'medium': passwordChangeData.passwordStrength === 3 || passwordChangeData.passwordStrength === 4,
                    'strong': passwordChangeData.passwordStrength === 5
                  }"
                  :style="{ width: (passwordChangeData.passwordStrength * 20) + '%' }"
                ></div>
              </div>
              <span class="strength-text">
                {{
                  passwordChangeData.passwordStrength <= 2 ? '弱' :
                  passwordChangeData.passwordStrength <= 4 ? '中' : '强'
                }}
              </span>
            </div>
          </div>

          <!-- 确认新密码 -->
          <div class="form-group">
            <label class="form-label">确认新密码</label>
            <div class="password-input-wrapper">
              <input
                :type="passwordChangeData.showConfirmPassword ? 'text' : 'password'"
                v-model="passwordChangeData.confirmPassword"
                placeholder="请再次输入新密码"
                class="password-input"
                autocomplete="new-password"
              />
              <button
                type="button"
                class="password-toggle"
                @click="togglePasswordVisibility('confirm')"
              >
                {{ passwordChangeData.showConfirmPassword ? '👁️' : '👁️‍🗨️' }}
              </button>
            </div>

            <!-- 密码匹配提示 -->
            <div
              v-if="passwordChangeData.confirmPassword"
              class="password-match"
              :class="{
                'match': passwordChangeData.newPassword === passwordChangeData.confirmPassword,
                'mismatch': passwordChangeData.newPassword !== passwordChangeData.confirmPassword
              }"
            >
              {{
                passwordChangeData.newPassword === passwordChangeData.confirmPassword
                  ? '✓ 密码匹配'
                  : '✗ 密码不匹配'
              }}
            </div>
          </div>

          <!-- 密码要求 -->
          <div class="password-requirements">
            <h4 class="requirements-title">密码要求：</h4>
            <ul class="requirements-list">
              <li :class="{ 'met': passwordChangeData.passwordRequirements.minLength }">
                至少8个字符
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasUppercase }">
                包含大写字母
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasLowercase }">
                包含小写字母
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasNumber }">
                包含数字
              </li>
              <li :class="{ 'met': passwordChangeData.passwordRequirements.hasSpecialChar }">
                包含特殊字符
              </li>
            </ul>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="password-change-footer">
          <button class="password-btn cancel" @click="closePasswordChangeModal">
            取消
          </button>
          <button class="password-btn confirm" @click="confirmPasswordChange">
            确认修改
          </button>
        </div>
      </div>
    </div>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import {
  NButton,
  NDropdown,
  NSpace,
  NGrid,
  NGridItem,
  NIcon,
  NTime,
  NTag,
  useMessage,
  useDialog,
  useNotification
} from 'naive-ui'
import { useAuthStore } from '../stores/auth'
import { notification } from '../utils/notification'
import ShiftHandoverModal from '../views/model/ShiftHandoverModal/index.vue'
import CurrentEntryDataModal from '../views/model/CurrentEntryDataModal/index.vue'
import BatchPlateEditModal from '../views/model/BatchPlateEditModal/index.vue'
// import ChargeDetailsModal from './ChargeDetailsModal.vue'
// import EntrySettingsModal from './EntrySettingsModal.vue'

interface Props {
  pageTitle?: string
  pageIcon?: string
  statusLabel: string
  statusValue: string
  isRefreshing?: boolean
  currentBooth?: string
}

const props = withDefaults(defineProps<Props>(), {
  pageTitle: '',
  pageIcon: '',
  isRefreshing: false,
  currentBooth: 'exit'
})

const emit = defineEmits<{
  refresh: []
  toggleChannelList: []
  switchBooth: [boothType: string]
}>()

const router = useRouter()
const authStore = useAuthStore()

// Naive UI 钩子
const message = useMessage()
const dialog = useDialog()
const naiveNotification = useNotification()

// 响应式数据
const showMenu = ref(false)
const showUserMenu = ref(false)
const showChannelList = ref(false)
const showLogoutModal = ref(false)
const showParkingModal = ref(false)
const showShiftHandoverModal = ref(false)
const showEntrySettingsModal = ref(false)
const showMonitorSettingsModal = ref(false)
const showChargeDetailsModal = ref(false)
const showPlateModificationModal = ref(false)
const showVehicleEntryModal = ref(false)
const showPasswordChangeModal = ref(false)
const currentTime = ref('')



// 用户下拉菜单选项
const userMenuOptions = [
  {
    label: '监控设置',
    key: 'monitor',
    icon: () => '🖥️'
  },
  {
    label: '进出口设置',
    key: 'entry',
    icon: () => '🚪'
  },
  {
    label: '收费明细',
    key: 'charge',
    icon: () => '💰'
  },
  {
    label: '车牌修改',
    key: 'plate',
    icon: () => '🚗'
  },
  {
    type: 'divider',
    key: 'd1'
  },
  {
    label: '修改密码',
    key: 'password',
    icon: () => '🔒'
  },
  {
    label: '交班',
    key: 'shift',
    icon: () => '🔄'
  },
  {
    label: '退出登录',
    key: 'logout',
    icon: () => '🚪'
  }
]



// 停车区域数据
const parkingAreas = ref([
  {
    id: 1,
    name: '私家车区域',
    totalSpaces: 300,
    remainingSpaces: 250
  },
  {
    id: 2,
    name: '电单车区域',
    totalSpaces: 300,
    remainingSpaces: 127
  },
  {
    id: 3,
    name: '地库',
    totalSpaces: 100,
    remainingSpaces: 94
  }
])



// 交班数据现在由ShiftHandoverModal组件管理

// 移除未使用的进出口设置数据，已迁移到 EntrySettingsModal 组件

// 监控设置数据
const monitorSettingsData = ref({
  selectedMode: '无监控模式',
  independentWindow: false,
  modes: [
    { value: '无监控模式', label: '无监控模式' },
    { value: '2屏', label: '2屏' },
    { value: '4屏', label: '4屏' },
    { value: '6屏', label: '6屏' },
    { value: '9屏', label: '9屏' },
    { value: '12屏', label: '12屏' }
  ]
})





// 车辆进入明细数据
const vehicleEntryData = ref({
  searchFilters: {
    startDate: '2025-07-25',
    endDate: '2025-07-25',
    startTime: '00:00:00',
    endTime: '23:59:59',
    plateNumber: '',
    entryMethod: '全部进入方式',
    vehicleType: '全部车辆类型',
    paymentStatus: '全部付款状态',
    entryChannel: '全部通道'
  },
  entryMethodOptions: ['全部进入方式', '刷卡进入', '扫码进入', '人工放行', '免费进入'],
  vehicleTypeOptions: ['全部车辆类型', '小型车', '大型车', '摩托车', '其他'],
  paymentStatusOptions: ['全部付款状态', '未付款', '已付款', '免费'],
  entryChannelOptions: ['全部通道', '通道1', '通道2', '通道3', '通道4'],
  records: [
    {
      id: 1,
      plateNumber: 'M-234',
      entryTime: '2025-07-25 14:14:14',
      entryMethod: '刷卡进入',
      vehicleType: '小型车',
      paymentStatus: '未付款',
      entryChannel: '通道1',
      operator: '停车场管理员001',
      remarks: 'edit',
      actions: '车辆进入'
    },
    {
      id: 2,
      plateNumber: 'G-123456',
      entryTime: '2025-07-25 17:32:24',
      entryMethod: '扫码进入',
      vehicleType: '小型车',
      paymentStatus: '未付款',
      entryChannel: '通道2',
      operator: '系统自动',
      remarks: 'edit',
      actions: '车辆进入'
    },
    {
      id: 3,
      plateNumber: '粤B12345',
      entryTime: '2025-07-25 10:15:14',
      entryMethod: '人工放行',
      vehicleType: '大型车',
      paymentStatus: '免费',
      entryChannel: '通道3',
      operator: '停车场管理员002',
      remarks: 'edit',
      actions: '车辆进入'
    }
  ]
})

// 密码修改数据
const passwordChangeData = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: '',
  showCurrentPassword: false,
  showNewPassword: false,
  showConfirmPassword: false,
  passwordStrength: 0,
  passwordRequirements: {
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecialChar: false
  }
})

// 移除未使用的变量以优化性能

// 方法 - 优化时间更新性能
let timeCache = ''
let lastSecond = -1

const updateTime = () => {
  const now = new Date()
  const currentSecond = now.getSeconds()

  // 只有秒数变化时才重新格式化，避免不必要的计算
  if (currentSecond !== lastSecond) {
    timeCache = now.toLocaleString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
    lastSecond = currentSecond
    currentTime.value = timeCache
  }
}

const toggleMenu = () => {
  showMenu.value = !showMenu.value
  if (showMenu.value) {
    showUserMenu.value = false
  }
}

const toggleUserMenu = (event?: Event) => {
  if (showUserMenu.value) {
    // 如果要关闭菜单，直接关闭
    showUserMenu.value = false
    return
  }

  // 打开菜单时，先设置位置再显示
  showMenu.value = false

  const target = event?.target as HTMLElement
  const userContainer = target?.closest('.user-container') as HTMLElement
  const dropdown = userContainer?.querySelector('.user-dropdown') as HTMLElement

  if (userContainer && dropdown) {
    const rect = userContainer.getBoundingClientRect()

    // 立即设置位置，避免从底部出现
    dropdown.style.top = `${rect.bottom + 8}px`
    dropdown.style.right = `${window.innerWidth - rect.right}px`
    dropdown.style.left = 'auto'
    dropdown.style.position = 'fixed'

    // 然后显示菜单
    showUserMenu.value = true
  }
}

const toggleChannelList = () => {
  showChannelList.value = !showChannelList.value
  emit('toggleChannelList')
}

// 岗亭切换方法
const switchToBooth = (boothType: string) => {
  emit('switchBooth', boothType)
  console.log(`切换到${boothType === 'exit' ? '出口' : '中央'}岗亭`)
}



const navigateTo = (path: string) => {
  router.push(path)
  showMenu.value = false
}

const handleMenuClick = (item: any) => {
  showMenu.value = false
  if (item.action) {
    // 触发自定义事件
    emit(item.action)
  } else {
    navigateTo(item.path)
  }
}

const refreshData = () => {
  emit('refresh')
}



// 处理用户菜单选择
const handleUserMenuSelect = (key: string) => {
  showUserMenu.value = false

  switch (key) {
    case 'monitor':
      viewMonitorSettings()
      break
    case 'entry':
      viewEntrySettings()
      break
    case 'charge':
      viewChargeDetails()
      break
    case 'plate':
      viewPlateModification()
      break
    case 'password':
      viewPasswordChange()
      break
    case 'shift':
      viewShiftHandover()
      break
    case 'logout':
      handleLogout()
      break
  }
}

// 移除未使用的方法以优化性能

// logout方法已合并到handleLogout中

// 新增的功能方法
const viewMonitorSettings = () => {
  showUserMenu.value = false
  showMonitorSettingsModal.value = true
}

const closeMonitorSettingsModal = () => {
  showMonitorSettingsModal.value = false
}

const selectMonitorMode = (mode: string) => {
  monitorSettingsData.value.selectedMode = mode
}

const toggleIndependentWindow = () => {
  monitorSettingsData.value.independentWindow = !monitorSettingsData.value.independentWindow
}

const cancelMonitorSettings = () => {
  // 重置为默认设置
  monitorSettingsData.value.selectedMode = '无监控模式'
  monitorSettingsData.value.independentWindow = false
  closeMonitorSettingsModal()
}

const confirmMonitorSettings = () => {
  // 应用监控设置
  closeMonitorSettingsModal()

  let message = `监控模式已设置为: ${monitorSettingsData.value.selectedMode}`
  if (monitorSettingsData.value.independentWindow) {
    message += '，独立窗口模式已启用'
  }

  notification.success(message, '设置成功')
  console.log('监控设置已应用:', {
    mode: monitorSettingsData.value.selectedMode,
    independentWindow: monitorSettingsData.value.independentWindow
  })
}

const viewEntrySettings = () => {
  showUserMenu.value = false
  showEntrySettingsModal.value = true
}



const closeEntrySettingsModal = () => {
  showEntrySettingsModal.value = false
}

const handleEntrySettingsSave = (channels: any[]) => {
  console.log('保存进出口设置:', channels)
  notification.success('进出口设置已保存', '系统提示')
}

// 移除未使用的进出口设置相关函数，已迁移到 EntrySettingsModal 组件

const viewChargeDetails = () => {
  showUserMenu.value = false
  showChargeDetailsModal.value = true
}



const handleLogout = () => {
  showUserMenu.value = false

  dialog.warning({
    title: '确认注销',
    content: '您确定要注销当前账户吗？注销后需要重新登录才能继续使用系统',
    positiveText: '确认注销',
    negativeText: '取消',
    onPositiveClick: () => {
      performLogout()
    }
  })
}

const cancelLogout = () => {
  showLogoutModal.value = false
}

const performLogout = async () => {
  try {
    await authStore.logout()
    message.success('注销成功')
    router.push('/login')
  } catch (error) {
    console.error('注销失败:', error)
    message.error('注销失败，请重试')
  }
}



// 停车位弹窗相关方法
const openParkingModal = () => {
  showParkingModal.value = true
}

const closeParkingModal = () => {
  showParkingModal.value = false
}

const modifyAreaSpaces = (area: any) => {
  // 验证输入值
  if (area.remainingSpaces < 0) {
    area.remainingSpaces = 0
    notification.warning('剩余车位数不能小于0', '输入错误')
    return
  }

  if (area.remainingSpaces > area.totalSpaces) {
    area.remainingSpaces = area.totalSpaces
    notification.warning('剩余车位数不能大于总车位数', '输入错误')
    return
  }

  // 这里可以调用API保存数据
  console.log(`修改${area.name}剩余车位为: ${area.remainingSpaces}`)
  notification.success(`${area.name}剩余车位已修改为: ${area.remainingSpaces}`, '修改成功')
}

const viewVehicleEntry = () => {
  showUserMenu.value = false
  showVehicleEntryModal.value = true
}

const closeVehicleEntryModal = () => {
  showVehicleEntryModal.value = false
}

const searchVehicleEntry = () => {
  // 根据筛选条件搜索车辆进入数据
  console.log('搜索车辆进入明细:', vehicleEntryData.value.searchFilters)
  notification.success('车辆进入数据已更新', '搜索成功')
}

const exportVehicleEntry = () => {
  // 导出车辆进入数据
  console.log('导出车辆进入数据')
  notification.success('车辆进入数据导出成功', '导出完成')
}

const editVehicleEntry = (recordId: number) => {
  // 编辑车辆进入记录
  console.log('编辑车辆进入记录:', recordId)
  notification.info('编辑功能开发中', '提示')
}

const viewPasswordChange = () => {
  showUserMenu.value = false
  showPasswordChangeModal.value = true
  // 重置密码修改表单
  passwordChangeData.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    showCurrentPassword: false,
    showNewPassword: false,
    showConfirmPassword: false,
    passwordStrength: 0,
    passwordRequirements: {
      minLength: false,
      hasUppercase: false,
      hasLowercase: false,
      hasNumber: false,
      hasSpecialChar: false
    }
  }
}

const closePasswordChangeModal = () => {
  showPasswordChangeModal.value = false
}

const togglePasswordVisibility = (field: string) => {
  // 切换密码显示/隐藏
  switch (field) {
    case 'current':
      passwordChangeData.value.showCurrentPassword = !passwordChangeData.value.showCurrentPassword
      break
    case 'new':
      passwordChangeData.value.showNewPassword = !passwordChangeData.value.showNewPassword
      break
    case 'confirm':
      passwordChangeData.value.showConfirmPassword = !passwordChangeData.value.showConfirmPassword
      break
  }
}

const checkPasswordStrength = (password: string) => {
  // 检查密码强度
  const requirements = passwordChangeData.value.passwordRequirements

  requirements.minLength = password.length >= 8
  requirements.hasUppercase = /[A-Z]/.test(password)
  requirements.hasLowercase = /[a-z]/.test(password)
  requirements.hasNumber = /\d/.test(password)
  requirements.hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password)

  const score = Object.values(requirements).filter(Boolean).length
  passwordChangeData.value.passwordStrength = score

  return score
}

const validatePasswords = () => {
  // 验证密码
  const { currentPassword, newPassword, confirmPassword } = passwordChangeData.value

  if (!currentPassword) {
    notification.error('请输入当前密码', '验证失败')
    return false
  }

  if (!newPassword) {
    notification.error('请输入新密码', '验证失败')
    return false
  }

  if (newPassword.length < 8) {
    notification.error('新密码长度至少8位', '验证失败')
    return false
  }

  if (newPassword !== confirmPassword) {
    notification.error('两次输入的新密码不一致', '验证失败')
    return false
  }

  if (currentPassword === newPassword) {
    notification.error('新密码不能与当前密码相同', '验证失败')
    return false
  }

  const strength = checkPasswordStrength(newPassword)
  if (strength < 3) {
    notification.warning('密码强度较弱，建议包含大小写字母、数字和特殊字符', '安全提示')
  }

  return true
}

const confirmPasswordChange = async () => {
  // 确认密码修改
  if (!validatePasswords()) {
    return
  }

  try {
    // 调用API修改密码
    const success = await authStore.changePassword(
      passwordChangeData.value.currentPassword,
      passwordChangeData.value.newPassword
    )

    if (success) {
      closePasswordChangeModal()
      notification.success('密码修改成功，请重新登录', '修改成功')

      // 延迟2秒后自动退出登录
      setTimeout(() => {
        authStore.logout()
        router.push('/login')
      }, 2000)
    } else {
      notification.error('密码修改失败，请检查当前密码是否正确', '修改失败')
    }
  } catch (error: any) {
    console.error('修改密码失败:', error)
    const errorMsg = error.message || '密码修改失败，请稍后重试'
    notification.error(errorMsg, '修改失败')
  }
}

const viewShiftHandover = () => {
  showUserMenu.value = false
  showShiftHandoverModal.value = true
}

const handleShiftHandoverConfirm = () => {
  // 处理交班确认
  showShiftHandoverModal.value = false
  notification.success('交班成功', '系统提示')
  console.log('执行交班操作')
}

const viewPlateModification = () => {
  showUserMenu.value = false
  showPlateModificationModal.value = true
}

const handleBatchPlateEdit = (data: { plateIds: number[]; newPlateNumber: string }) => {
  console.log('批量修改车牌:', data)
  notification.success(`成功修改${data.plateIds.length}个车牌为: ${data.newPlateNumber}`, '批量修改成功')
  showPlateModificationModal.value = false
}



// 点击外部关闭菜单
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.menu-container')) {
    showMenu.value = false
  }
  if (!target.closest('.user-container')) {
    showUserMenu.value = false
  }
}

// 定时器引用
let timeInterval: number | null = null

onMounted(() => {
  // 初始化时间
  updateTime()
  timeInterval = setInterval(updateTime, 1000)

  // 添加点击外部事件监听
  document.addEventListener('click', handleClickOutside)
})

// 清理函数
onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
    timeInterval = null
  }
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.unified-top-bar {
  height: 80px;
  background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  color: #ffffff;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

/* 移除shimmer动画以提升性能 */

/* 左侧区域 */
.top-left-section {
  display: flex;
  align-items: center;
  gap: 24px;
  flex: 1;
  z-index: 1;
}

/* 岗亭切换标签页样式 */
.booth-switch-container {
  margin-right: 16px;
}

.booth-switch-tabs {
  display: flex;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 2px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.booth-tab {
  padding: 6px 16px;
  font-size: 13px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  border-radius: 6px;
  transition: all 0.2s ease;
  white-space: nowrap;
  user-select: none;
}

.booth-tab:hover {
  color: rgba(255, 255, 255, 0.95);
  background: rgba(255, 255, 255, 0.1);
}

.booth-tab.active {
  background: rgba(255, 255, 255, 0.9);
  color: #1a73e8;
  font-weight: 600;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 响应式样式 */
@media (max-width: 768px) {
  .booth-tab {
    padding: 5px 12px;
    font-size: 12px;
  }

  .booth-switch-container {
    margin-right: 12px;
  }
}

.menu-container {
  position: relative;
}

.menu-button {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: #ffffff;
  padding: 10px 16px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  /* 移除backdrop-filter以提升性能 */
}

.menu-button:hover,
.menu-button.active {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.menu-icon {
  font-size: 18px;
  transition: transform 0.3s ease;
}

.menu-button:hover .menu-icon {
  transform: scale(1.1);
}

.menu-text {
  font-weight: 600;
}

.dropdown-arrow {
  font-size: 12px;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.8;
}

.dropdown-arrow.active {
  transform: rotate(180deg);
}

/* Logo区域 */
.logo-section {
  display: flex;
  align-items: center;
  gap: 32px;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  /* 移除backdrop-filter以提升性能 */
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logo-icon {
  font-size: 32px;
  /* 移除动画和复杂滤镜以提升性能 */
}

.logo-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-main {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-brand {
  font-size: 24px;
  font-weight: 800;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.logo-version {
  font-size: 12px;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 600;
  text-shadow: none;
}

.logo-subtitle {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.page-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.page-icon {
  font-size: 20px;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.page-title {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 中央Logo区域 */
.top-center-section {
  display: flex;
  justify-content: center;
  align-items: center;
  flex: 0 0 auto;
  z-index: 1;
}

.logo-container-center {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 8px 20px;
  /* 去掉透明底 */
  border-radius: 16px;
}

/* 右侧时间显示 */
.time-display-right {
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  /* 移除backdrop-filter以提升性能 */
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-right: 16px;
}

.current-time-only {
  font-size: 16px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

/* 右侧区域 */
.top-right-section {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
  z-index: 1;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.08);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.status-label {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.status-value {
  font-size: 16px;
  font-weight: 700;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  background: rgba(255, 255, 255, 0.12);
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: #ffffff;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  font-weight: 600;
  /* 移除backdrop-filter以提升性能 */
}

.action-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-btn.refresh {
  border-color: rgba(76, 175, 80, 0.5);
}

.action-btn.refresh:hover:not(:disabled) {
  background: rgba(76, 175, 80, 0.2);
}

.action-btn.back {
  border-color: rgba(255, 193, 7, 0.5);
}

.action-btn.back:hover {
  background: rgba(255, 193, 7, 0.2);
}

.btn-icon {
  font-size: 14px;
}

.btn-text {
  font-weight: 600;
}

/* 用户信息区域 */
.user-container {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 16px;
  background: transparent;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: none;
}

.user-info:hover,
.user-info.active {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(45deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.avatar-icon {
  font-size: 18px;
  color: white;
}

.user-details {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.username {
  font-size: 14px;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-role {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 500;
}

/* 下拉菜单样式 */
.menu-dropdown,
.user-dropdown {
  position: fixed;
  top: 0;
  right: 0;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.25);
  min-width: 220px;
  z-index: 100000;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px) scale(0.95);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  /* 移除backdrop-filter以提升性能 */
  border: 1px solid rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.menu-dropdown {
  left: 0;
  min-width: 240px;
}

.user-dropdown {
  right: 0;
  min-width: 400px;
  max-height: 80vh;
  overflow-y: auto;
}

.menu-dropdown.show,
.user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0) scale(1);
}

.menu-item,
.user-dropdown-item {
  padding: 14px 18px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 500;
  position: relative;
  overflow: hidden;
}

.menu-item::before,
.user-dropdown-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transition: width 0.3s ease;
  z-index: 0;
}

.menu-item:hover::before,
.user-dropdown-item:hover::before {
  width: 4px;
}

.menu-item:hover,
.user-dropdown-item:hover {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  color: #1e3c72;
  transform: translateX(4px);
}

.menu-item:first-child,
.user-dropdown-item:first-child {
  border-radius: 12px 12px 0 0;
}

.menu-item:last-child,
.user-dropdown-item:last-child {
  border-radius: 0 0 12px 12px;
  border-bottom: none;
}

.menu-item-icon,
.dropdown-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
  z-index: 1;
  position: relative;
}

.user-dropdown-item.logout {
  color: #dc3545;
  border-top: 1px solid rgba(220, 53, 69, 0.1);
}

.user-dropdown-item.logout::before {
  background: linear-gradient(90deg, #dc3545, #c82333);
}

.user-dropdown-item.logout:hover {
  background: linear-gradient(90deg, rgba(220, 53, 69, 0.05), rgba(200, 35, 51, 0.05));
  color: #721c24;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .unified-top-bar {
    padding: 0 16px;
  }

  .logo-container {
    padding: 6px 16px;
  }

  .logo-brand {
    font-size: 20px;
  }

  .page-title {
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .unified-top-bar {
    height: 70px;
    padding: 0 12px;
  }

  .top-left-section {
    gap: 16px;
  }

  .logo-container {
    padding: 4px 12px;
  }

  .logo-brand {
    font-size: 18px;
  }

  .logo-subtitle {
    display: none;
  }

  .current-time {
    font-size: 20px;
  }

  .time-display {
    padding: 8px 16px;
  }

  .status-item {
    display: none;
  }

  .action-buttons {
    gap: 4px;
  }

  .action-btn {
    padding: 6px 8px;
    font-size: 12px;
  }

  .btn-text {
    display: none;
  }
}

/* 用户下拉菜单新样式 */
.user-dropdown-header {
  padding: 16px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 12px 12px 0 0;
  margin: -1px -1px 0 -1px;
}

.user-avatar-large {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 16px;
  font-weight: 600;
}

.avatar-text {
  color: white;
}

.dropdown-arrow-user {
  font-size: 12px;
  opacity: 0.8;
}

.offline-data-section {
  padding: 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
  text-align: center;
}

.data-grid {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-row {
  display: flex;
  gap: 12px;
}

.data-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: white;
  border-radius: 6px;
  border: 1px solid #e9ecef;
  min-height: 36px;
}

.data-label {
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.data-value {
  font-size: 12px;
  color: #333;
  font-weight: 600;
}

.function-buttons {
  padding: 20px;
  background: white;
}

.button-row {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.button-row:last-child {
  margin-bottom: 0;
}

.function-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  border: 1px solid #e1e5e9;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
  font-weight: 500;
  min-height: 60px;
  background: #ffffff;
  color: #495057;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.function-btn.primary {
  border-color: #4a90e2;
  color: #4a90e2;
}

.function-btn.secondary {
  border-color: #6c757d;
  color: #6c757d;
}

.function-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-color: #4a90e2;
  color: #4a90e2;
}

.function-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.function-btn .btn-icon {
  font-size: 18px;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.function-btn:hover .btn-icon {
  opacity: 1;
}

.function-btn span:last-child {
  font-size: 11px;
  text-align: center;
  line-height: 1.2;
  font-weight: 500;
}

/* 美化的注销确认弹窗样式 */
.logout-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  /* 移除backdrop-filter以提升性能 */
  animation: fadeIn 0.3s ease-out;
}

.logout-modal {
  background: white;
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  min-width: 400px;
  max-width: 500px;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.logout-modal-header {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
  padding: 24px;
  text-align: center;
  position: relative;
}

.logout-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 16px;
  /* 移除backdrop-filter以提升性能 */
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.logout-icon-symbol {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logout-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logout-modal-body {
  padding: 32px 24px;
  text-align: center;
}

.logout-message {
  font-size: 16px;
  color: #333;
  margin: 0 0 12px 0;
  font-weight: 500;
}

.logout-submessage {
  font-size: 14px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.logout-modal-footer {
  padding: 20px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
  background: #f8f9fa;
}

.logout-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;
  justify-content: center;
}

.logout-btn.cancel {
  background: #6c757d;
  color: white;
}

.logout-btn.cancel:hover {
  background: #5a6268;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
}

.logout-btn.confirm {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
  color: white;
}

.logout-btn.confirm:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d84315 100%);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(255, 107, 107, 0.4);
}

.logout-btn .btn-icon {
  font-size: 16px;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 停车位弹窗样式 */
.parking-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  /* 移除backdrop-filter以提升性能 */
  animation: fadeIn 0.3s ease-out;
}

.parking-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  min-width: 600px;
  max-width: 800px;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.parking-modal-header {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  padding: 20px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.parking-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.close-btn {
  background: none;
  border: none;
  color: white;
  font-size: 20px;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background 0.3s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.parking-modal-body {
  padding: 24px;
}

.parking-table {
  width: 100%;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e9ecef;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.header-cell {
  padding: 16px 12px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  border-right: 1px solid #e9ecef;
}

.header-cell:last-child {
  border-right: none;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1fr;
  border-bottom: 1px solid #e9ecef;
  transition: background 0.3s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 16px 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid #e9ecef;
}

.table-cell:last-child {
  border-right: none;
}

.area-name {
  font-weight: 500;
  color: #333;
}

.total-spaces {
  font-weight: 600;
  color: #28a745;
}

.remaining-spaces {
  padding: 8px 12px;
}

.spaces-input {
  width: 80px;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 6px;
  text-align: center;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  transition: border-color 0.3s ease;
}

.spaces-input:focus {
  outline: none;
  border-color: #4a90e2;
  box-shadow: 0 0 0 2px rgba(74, 144, 226, 0.2);
}

.modify-btn {
  background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.modify-btn:hover {
  background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(74, 144, 226, 0.3);
}

/* 让剩余车位可点击 */
.status-item {
  cursor: pointer;
  transition: all 0.3s ease;
}

.status-item:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  transform: translateY(-1px);
}

/* 特殊按钮样式 - 重要功能 */
.function-btn.important {
  border-color: #28a745;
  color: #28a745;
}

.function-btn.important:hover {
  border-color: #28a745;
  color: #28a745;
  background: rgba(40, 167, 69, 0.05);
}

/* 警告类按钮样式 */
.function-btn.warning {
  border-color: #ffc107;
  color: #856404;
}

.function-btn.warning:hover {
  border-color: #ffc107;
  color: #856404;
  background: rgba(255, 193, 7, 0.05);
}

/* 交班样式现在由ShiftHandoverModal组件管理 */



/* 进出口设置弹窗样式已移至 EntrySettingsModal.vue */



/* 监控设置弹窗样式 */
.monitor-settings-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.monitor-settings-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 800px;
  max-width: 900px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.monitor-settings-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.monitor-settings-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.monitor-settings-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.monitor-notice {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  padding: 12px 16px;
  margin-bottom: 24px;
  font-size: 14px;
  color: #856404;
  line-height: 1.5;
}

.monitor-modes {
  display: flex;
  align-items: center;
  gap: 0;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.mode-btn {
  padding: 12px 20px;
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-right: none;
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.mode-btn:first-child {
  border-radius: 6px 0 0 6px;
}

.mode-btn:nth-child(6) {
  border-right: 1px solid #dee2e6;
  border-radius: 0 6px 6px 0;
}

.mode-btn.active {
  background: #007bff;
  color: #ffffff;
  border-color: #007bff;
}

.mode-btn:hover:not(.active) {
  background: #e9ecef;
  color: #495057;
}

.independent-window-option {
  margin-left: 24px;
  display: flex;
  align-items: center;
}

.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #495057;
}

.checkbox-input {
  width: 18px;
  height: 18px;
  border: 2px solid #dee2e6;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkbox-input:checked {
  background: #007bff;
  border-color: #007bff;
}

.checkbox-label {
  font-weight: 500;
  user-select: none;
}

.monitor-display-area {
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  min-height: 300px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: all 0.3s ease;
}

.monitor-placeholder {
  text-align: center;
  color: #6c757d;
}

.placeholder-text {
  display: block;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
}

.placeholder-mode {
  display: block;
  font-size: 14px;
  color: #007bff;
  font-weight: 600;
}

.monitor-settings-footer {
  padding: 20px 0 0 0;
  border-top: 1px solid #e1e5e9;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.monitor-btn {
  padding: 10px 32px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.monitor-btn.cancel {
  background: #ffffff;
  border-color: #6c757d;
  color: #6c757d;
}

.monitor-btn.cancel:hover {
  background: #6c757d;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.monitor-btn.confirm {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.monitor-btn.confirm:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .monitor-settings-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .monitor-settings-header {
    padding: 16px 20px;
  }

  .monitor-settings-body {
    padding: 20px;
  }

  .monitor-modes {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .mode-btn {
    border-radius: 6px !important;
    border-right: 1px solid #dee2e6 !important;
    margin-bottom: 8px;
  }

  .independent-window-option {
    margin-left: 0;
    justify-content: center;
  }

  .monitor-display-area {
    min-height: 200px;
  }

  .monitor-settings-footer {
    flex-direction: column;
  }

  .monitor-btn {
    width: 100%;
  }
}

/* 收费明细样式现在由CurrentEntryDataModal组件管理 */



























/* 车辆进入明细弹窗样式 */
.vehicle-entry-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.vehicle-entry-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 1200px;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.vehicle-entry-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.vehicle-entry-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.vehicle-entry-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.entry-filter-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.filter-row {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 16px;
  flex-wrap: wrap;
}

.filter-row:last-child {
  margin-bottom: 0;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  min-width: 80px;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.date-input {
  width: 140px;
}

.time-input {
  width: 100px;
}

.filter-separator {
  font-size: 16px;
  color: #6c757d;
  font-weight: 500;
  margin: 0 8px;
}

.filter-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #dee2e6;
}

.action-checkboxes {
  display: flex;
  gap: 20px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
}

.checkbox-input {
  width: 16px;
  height: 16px;
  border: 2px solid #dee2e6;
  border-radius: 3px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.checkbox-input:checked {
  background: #007bff;
  border-color: #007bff;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.vehicle-entry-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.header-cell {
  padding: 12px 8px;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  flex-shrink: 0;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.plate-number {
  width: 120px;
}

.header-cell.entry-time {
  width: 140px;
}

.header-cell.entry-method {
  width: 100px;
}

.header-cell.vehicle-type {
  width: 100px;
}

.header-cell.payment-status {
  width: 100px;
}

.header-cell.entry-channel {
  width: 100px;
}

.header-cell.operator {
  width: 140px;
}

.header-cell.remarks {
  width: 80px;
}

.header-cell.actions {
  width: 100px;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px 8px;
  font-size: 12px;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.plate-number {
  width: 120px;
  font-weight: 500;
}

.table-cell.entry-time {
  width: 140px;
  font-size: 11px;
}

.table-cell.entry-method {
  width: 100px;
}

.table-cell.vehicle-type {
  width: 100px;
}

.table-cell.payment-status {
  width: 100px;
}

.table-cell.entry-channel {
  width: 100px;
}

.table-cell.operator {
  width: 140px;
  font-size: 11px;
}

.table-cell.remarks {
  width: 80px;
}

.table-cell.actions {
  width: 100px;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.未付款 {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

.status-badge.已付款 {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status-badge.免费 {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.action-link {
  background: none;
  border: none;
  color: #007bff;
  font-size: 12px;
  cursor: pointer;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.action-link:hover {
  color: #0056b3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .vehicle-entry-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .vehicle-entry-header {
    padding: 16px 20px;
  }

  .vehicle-entry-body {
    padding: 20px;
  }

  .entry-filter-section {
    padding: 16px;
  }

  .filter-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
  }

  .filter-group label {
    min-width: auto;
  }

  .filter-actions {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .action-checkboxes {
    justify-content: center;
  }

  .action-buttons {
    justify-content: center;
  }

  .vehicle-entry-table {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    min-width: 1000px;
  }
}

/* 密码修改弹窗样式 */
.password-change-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.password-change-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 500px;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.password-change-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.password-change-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.password-change-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.password-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #495057;
  margin-bottom: 8px;
}

.password-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.password-input {
  width: 100%;
  padding: 12px 16px;
  padding-right: 50px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: #ffffff;
}

.password-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

.password-toggle {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  font-size: 16px;
  cursor: pointer;
  color: #6c757d;
  transition: color 0.3s ease;
  padding: 4px;
}

.password-toggle:hover {
  color: #495057;
}

.password-strength {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.weak {
  background: #dc3545;
}

.strength-fill.medium {
  background: #ffc107;
}

.strength-fill.strong {
  background: #28a745;
}

.strength-text {
  font-size: 12px;
  font-weight: 500;
  min-width: 20px;
}

.password-match {
  margin-top: 8px;
  font-size: 12px;
  font-weight: 500;
}

.password-match.match {
  color: #28a745;
}

.password-match.mismatch {
  color: #dc3545;
}

.password-requirements {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  padding: 16px;
  margin-top: 16px;
}

.requirements-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.requirements-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.requirements-list li {
  font-size: 13px;
  color: #6c757d;
  margin-bottom: 6px;
  padding-left: 20px;
  position: relative;
  transition: color 0.3s ease;
}

.requirements-list li:last-child {
  margin-bottom: 0;
}

.requirements-list li:before {
  content: '○';
  position: absolute;
  left: 0;
  color: #dee2e6;
  transition: all 0.3s ease;
}

.requirements-list li.met {
  color: #28a745;
}

.requirements-list li.met:before {
  content: '✓';
  color: #28a745;
}

.password-change-footer {
  display: flex;
  gap: 12px;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #e1e5e9;
}

.password-btn {
  padding: 10px 32px;
  border: 1px solid;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.password-btn.cancel {
  background: #ffffff;
  border-color: #6c757d;
  color: #6c757d;
}

.password-btn.cancel:hover {
  background: #6c757d;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

.password-btn.confirm {
  background: #007bff;
  border-color: #007bff;
  color: #ffffff;
}

.password-btn.confirm:hover {
  background: #0056b3;
  border-color: #0056b3;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.password-btn.confirm:disabled {
  background: #6c757d;
  border-color: #6c757d;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .password-change-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .password-change-header {
    padding: 16px 20px;
  }

  .password-change-body {
    padding: 20px;
  }

  .password-change-footer {
    flex-direction: column;
  }

  .password-btn {
    width: 100%;
  }
}

/* 入口列表按钮美化样式 */
.entrance-list-btn {
  background: linear-gradient(135deg, var(--blue-primary) 0%, var(--blue-hover) 100%);
  border: none;
  color: white;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 8px rgba(25, 118, 210, 0.3);
  position: relative;
  overflow: hidden;
}

.entrance-list-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.entrance-list-btn:hover::before {
  left: 100%;
}

.entrance-list-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(25, 118, 210, 0.4);
}

.entrance-list-btn.active {
  background: linear-gradient(135deg, var(--blue-pressed) 0%, var(--blue-hover) 100%);
  box-shadow: 0 4px 12px rgba(13, 71, 161, 0.5);
}



@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.entrance-list-btn .btn-icon {
  font-size: 16px;
  margin-right: 4px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}
</style>
