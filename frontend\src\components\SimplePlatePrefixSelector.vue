<template>
  <div v-if="visible" class="plate-prefix-modal" @click="handleClose">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h3>选择车牌前缀</h3>
        <button class="close-btn" @click="handleClose">×</button>
      </div>
      
      <div class="modal-body">
        <!-- 简化的前缀网格 -->
        <div class="prefix-grid">
          <button
            v-for="prefix in allPrefixes"
            :key="prefix.code"
            class="prefix-btn"
            :class="{ active: modelValue === prefix.code }"
            @click="selectPrefix(prefix.code)"
          >
            {{ prefix.code }}
          </button>
        </div>
      </div>
      
      <div class="modal-footer">
        <button class="cancel-btn" @click="handleClose">取消</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  visible: boolean
  modelValue: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  modelValue: ''
})

const emit = defineEmits<{
  'update:visible': [value: boolean]
  'update:model-value': [value: string]
  'confirm': [value: string]
}>()

// 简化的车牌前缀数据
const allPrefixes = [
  { code: '京' }, { code: '津' }, { code: '沪' }, { code: '渝' },
  { code: '冀' }, { code: '豫' }, { code: '云' }, { code: '辽' },
  { code: '黑' }, { code: '湘' }, { code: '皖' }, { code: '鲁' },
  { code: '新' }, { code: '苏' }, { code: '浙' }, { code: '赣' },
  { code: '鄂' }, { code: '桂' }, { code: '甘' }, { code: '晋' },
  { code: '蒙' }, { code: '陕' }, { code: '吉' }, { code: '闽' },
  { code: '贵' }, { code: '粤' }, { code: '青' }, { code: '藏' },
  { code: '川' }, { code: '宁' }, { code: '琼' }
]

const selectPrefix = (code: string) => {
  emit('update:model-value', code)
  emit('confirm', code)
  emit('update:visible', false)
}

const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
.plate-prefix-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 70vh;
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
  max-height: 300px;
  overflow-y: auto;
}

.prefix-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 8px;
}

.prefix-btn {
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
  font-size: 16px;
  transition: background-color 0.2s;
}

.prefix-btn:hover {
  background: #f0f0f0;
}

.prefix-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.modal-footer {
  padding: 16px 20px;
  border-top: 1px solid #e0e0e0;
  text-align: right;
}

.cancel-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: white;
  cursor: pointer;
}

.cancel-btn:hover {
  background: #f0f0f0;
}
</style>
