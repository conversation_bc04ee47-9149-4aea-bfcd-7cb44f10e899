# 跨学批量修改车牌弹框 - 测试指南

## 测试环境
- 开发服务器: http://localhost:5176/
- 浏览器: 任意现代浏览器

## 测试步骤

### 1. 打开弹框
1. 访问 http://localhost:5176/
2. 在右侧控制面板中找到"跨学批量修改车牌"按钮
3. 点击按钮，应该弹出模态框

### 2. 界面验证
验证弹框界面是否符合设计要求：

#### 标题栏
- [x] 标题显示为"跨学批量修改车牌"
- [x] 右上角有关闭按钮

#### 搜索区域
- [x] 车牌号码输入框
- [x] 时间范围选择器（默认值：2025-06-22 17:47:33 到 2025-07-22 17:47:33）
- [x] 车牌颜色下拉框（全部）
- [x] 入口通道下拉框（全部）
- [x] 空车牌复选框
- [x] 搜索按钮
- [x] 重置按钮

#### 车牌网格
- [x] 5列网格布局
- [x] 每个车牌项包含：
  - 车辆类型标签（左上角）
  - 车牌图片或占位符
  - 车牌号码覆盖层（图片底部中央）
  - 车牌信息区域：
    - 车牌号码和颜色选择器
    - 时间和修改按钮

#### 分页区域
- [x] 简单的页码导航
- [x] 上一页/下一页按钮
- [x] 当前页高亮显示

### 3. 功能测试

#### 搜索功能
1. 在车牌号码输入框中输入"TT"
2. 点击搜索按钮
3. 验证是否显示加载状态
4. 验证搜索结果

#### 重置功能
1. 修改搜索条件
2. 点击重置按钮
3. 验证所有搜索条件是否重置为默认值

#### 车牌颜色修改
1. 点击任意车牌项的颜色下拉框
2. 选择不同的颜色
3. 验证是否更新成功

#### 单个修改
1. 点击任意车牌项的"修改"按钮
2. 验证控制台是否输出相关信息

#### 分页功能
1. 点击页码按钮
2. 验证页面是否切换
3. 验证当前页是否正确高亮

#### 关闭弹框
1. 点击右上角关闭按钮
2. 验证弹框是否关闭
3. 点击遮罩层（应该无法关闭，因为设置了 mask-closable="false"）

### 4. 样式验证

#### 布局检查
- [x] 弹框大小：95vw x 90vh
- [x] 网格布局：5列等宽
- [x] 车牌项间距：12px
- [x] 搜索区域背景色：#f8f9fa

#### 响应式检查
1. 调整浏览器窗口大小
2. 验证网格布局是否保持5列
3. 验证弹框是否适应屏幕大小

#### 视觉效果
- [x] 车牌图片占位符显示
- [x] 车牌号码覆盖层样式
- [x] 按钮样式和大小
- [x] 分页按钮的激活状态

### 5. 数据验证

#### 模拟数据
验证是否正确显示以下测试数据：
- TT7000 (普通车辆)
- TT2221 (普通车辆) 
- MX8549 (普通车辆)
- MK3226 (普通车辆)
- AA10005 (普通车辆，无图片)
- MZ1234 (内部车辆)
- MR1234 (内部车辆)
- MP1234 (内部车辆)
- AA10072 (内部车辆，无图片)
- AA10011 (内部车辆，无图片)

#### 时间格式
验证时间显示格式是否为：yyyy-MM-dd HH:mm

### 6. 控制台日志
打开浏览器开发者工具，验证以下操作是否有正确的控制台输出：
- 搜索操作
- 重置操作
- 车牌颜色修改
- 单个车牌修改

## 预期结果

### 成功标准
- [x] 弹框能正常打开和关闭
- [x] 界面布局符合设计要求
- [x] 所有交互功能正常工作
- [x] 样式显示正确
- [x] 数据显示完整
- [x] 控制台无错误信息

### 已知问题
- 图片URL使用占位符，实际环境需要替换为真实的API地址
- 搜索功能为模拟实现，实际环境需要连接后端API
- 批量修改功能需要在父组件中实现具体逻辑

## 下一步
1. 集成真实的后端API
2. 实现实际的搜索和修改功能
3. 添加错误处理和用户反馈
4. 优化性能和用户体验
