// 模拟监控数据
export interface MonitorData {
  id: string
  plateNumber: string
  timestamp: string
  status: string
  image: string
}

// 生成假的监控图片数据
export const mockMonitorData: MonitorData[] = [
  {
    id: 'TT7000',
    plateNumber: 'TT-70-00',
    timestamp: '2025-07-22 17:46:08',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=1'
  },
  {
    id: 'TT2221',
    plateNumber: 'TT-22-21',
    timestamp: '2025-07-22 14:08:22',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=2'
  },
  {
    id: 'MX8549',
    plateNumber: 'MX-85-49',
    timestamp: '2025-07-22 14:06:09',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=3'
  },
  {
    id: 'MK3226',
    plateNumber: 'MK-32-26',
    timestamp: '2025-07-22 11:57:47',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=4'
  },
  {
    id: 'AA10006',
    plateNumber: '',
    timestamp: '2025-07-21 15:08:04',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=5'
  },
  {
    id: 'MZ1234',
    plateNumber: 'MZ-12-34',
    timestamp: '2025-07-21 14:43:40',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=6'
  },
  {
    id: 'AB5678',
    plateNumber: 'AB-56-78',
    timestamp: '2025-07-21 13:25:15',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=7'
  },
  {
    id: 'CD9012',
    plateNumber: 'CD-90-12',
    timestamp: '2025-07-21 12:18:33',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=8'
  },
  {
    id: 'EF3456',
    plateNumber: 'EF-34-56',
    timestamp: '2025-07-21 11:45:22',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=9'
  },
  {
    id: 'GH7890',
    plateNumber: 'GH-78-90',
    timestamp: '2025-07-21 10:32:11',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=10'
  },
  {
    id: 'IJ1357',
    plateNumber: 'IJ-13-57',
    timestamp: '2025-07-21 09:28:45',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=11'
  },
  {
    id: 'KL2468',
    plateNumber: 'KL-24-68',
    timestamp: '2025-07-21 08:15:30',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=12'
  },
  {
    id: 'MN3691',
    plateNumber: 'MN-36-91',
    timestamp: '2025-07-20 17:42:18',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=13'
  },
  {
    id: 'OP4702',
    plateNumber: 'OP-47-02',
    timestamp: '2025-07-20 16:35:27',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=14'
  },
  {
    id: 'QR5813',
    plateNumber: 'QR-58-13',
    timestamp: '2025-07-20 15:22:14',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=15'
  },
  {
    id: 'ST6924',
    plateNumber: 'ST-69-24',
    timestamp: '2025-07-20 14:18:55',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=16'
  },
  {
    id: 'UV7035',
    plateNumber: 'UV-70-35',
    timestamp: '2025-07-20 13:45:33',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=17'
  },
  {
    id: 'WX8146',
    plateNumber: 'WX-81-46',
    timestamp: '2025-07-20 12:32:21',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=18'
  },
  {
    id: 'YZ9257',
    plateNumber: 'YZ-92-57',
    timestamp: '2025-07-20 11:28:09',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=19'
  },
  {
    id: 'BC0368',
    plateNumber: 'BC-03-68',
    timestamp: '2025-07-20 10:15:47',
    status: '智能入场',
    image: 'https://picsum.photos/240/180?random=20'
  }
]

// 获取分页数据的工具函数
export const getPagedMonitorData = (page: number, pageSize: number = 6) => {
  const start = (page - 1) * pageSize
  const end = start + pageSize
  return mockMonitorData.slice(start, end)
}

// 获取总页数
export const getTotalPages = (pageSize: number = 6) => {
  return Math.ceil(mockMonitorData.length / pageSize)
}

// 根据ID查找监控数据
export const findMonitorById = (id: string) => {
  return mockMonitorData.find(monitor => monitor.id === id)
}

// 搜索监控数据
export const searchMonitorData = (keyword: string) => {
  if (!keyword.trim()) {
    return mockMonitorData
  }
  
  const lowerKeyword = keyword.toLowerCase()
  return mockMonitorData.filter(monitor => 
    monitor.plateNumber.toLowerCase().includes(lowerKeyword) ||
    monitor.id.toLowerCase().includes(lowerKeyword) ||
    monitor.timestamp.includes(keyword)
  )
}
