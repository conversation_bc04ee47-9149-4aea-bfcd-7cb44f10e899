[{"ContainingType": "ParkingBoothApi.Controllers.HardwareController", "Method": "OpenCashDrawer", "RelativePath": "api/Hardware/cashdrawer/open", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "openDto", "Type": "ParkingBoothApi.DTOs.OpenCashDrawerDto", "IsRequired": false}], "ReturnTypes": [{"Type": "ParkingBoothApi.DTOs.CashDrawerResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ParkingBoothApi.Controllers.HardwareController", "Method": "TestCashDrawer", "RelativePath": "api/Hardware/cashdrawer/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ParkingBoothApi.DTOs.CashDrawerResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ParkingBoothApi.Controllers.HardwareController", "Method": "GetAvailablePrinters", "RelativePath": "api/Hardware/printer/list", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ParkingBoothApi.DTOs.PrinterListResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ParkingBoothApi.Controllers.HardwareController", "Method": "PrintTicket", "RelativePath": "api/Hardware/printer/print", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "printDto", "Type": "ParkingBoothApi.DTOs.PrintTicketDto", "IsRequired": true}], "ReturnTypes": [{"Type": "ParkingBoothApi.DTOs.PrinterResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ParkingBoothApi.Controllers.HardwareController", "Method": "GetPrinterStatus", "RelativePath": "api/Hardware/printer/status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ParkingBoothApi.DTOs.PrinterStatusDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "ParkingBoothApi.Controllers.HardwareController", "Method": "TestPrinter", "RelativePath": "api/Hardware/printer/test", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "ParkingBoothApi.DTOs.PrinterResponseDto", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_3", "RelativePath": "api/health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "<>f__AnonymousType1`3[[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.DateTime, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=9.0.0.0, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["application/json"], "StatusCode": 200}]}]