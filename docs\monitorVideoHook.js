import draggable from "vuedraggable";
import {
  Apis
} from "@/api";
import iHeaderFullscreen from "@/layouts/basic-layout/header-fullscreen";
import {
  sleep
} from "@/main.js";
import Base64 from "@/utils/base64.js"
import {
  Renderer
} from "@/utils/renderer.js"
import {
  findObjectInArray
} from "@/utils/js/findObjectByArray.js"
import dragHook from "./dragHook";
import myAlert from "../MyAlert.vue"
let hx_ren = {}
export default {
  props: ["channelBin", "userInfo", "closeDelay", "isaFullscreen", "isMoon", "typeLayout", "errorInfo"],
  mixins: [dragHook],
  components: {
    iHeaderFullscreen,
    draggable,
    myAlert
  },
  data() {
    return {
      storageKey: 'custom_sort_order', // 本地存储的key
      openDelay: 0, // 开闸延时 4000 毫秒
      type: 0,
      current: 0,
      drawerValue: false,
      list: [],
      qyList: [],
      hxList: [],
      dragStartIndex: null,
      dragOverIndex: null,
      isDragging: false,
      slidesPerView: 2,
      swiperOption: { //轮播属性
        loop: false, // 明确关闭循环
        slideToClickedSlide: true,
        centeredSlides: false, //居左
        slidesPerView: 2,
        grabCursor: true,
        keyboard: true,
        spaceBetween: 5,
        loopFillGroupWithBlank: false,
        simulateTouch: false,
        navigation: {
          nextEl: ".swiper-button-next",
          prevEl: ".swiper-button-prev"
        }
      },
      searchForm: {
        channelName: "",
        pageSize: 1000,
        searchType: 0,
        pageNum: 1,
      },
      ws: [],
      imgHeignt: "100%",
      imgWidth: "100%",
      camType: null,
      oldCamType: null,
      camUserName: "",
      camPwd: "",
      boundVisibilityHandler: null,
    };
  },
  watch: {
    isaFullscreen(n) {
      // 华夏相机不触发
      if (this.camType != 1) {
        // 窗体大小发生变变化时，重新加载视频
        this.list.forEach(async (item, index) => {
          if (item.cameraType != 1) {
            this.openOrReshoot(item.id, 'offOrOn')
            await sleep(500)
            this.openOrReshoot(item.id, 'offOrOn')
          }
        })
      }
    },
    async typeLayout(v) {
      this.slidesPerView = 0;
      await sleep(100);
      // 切换布局的时候 元素会发生变化导致 视捷相机不能播放，所以先关闭所有相机，再开启视频
      this.list.forEach(async (item, index) => {
        // if (this.camType !== 1) { // 华夏相机不重新播放
        this.openOrReshoot(item.id, 'offOrOn')
        await sleep(1000)
        this.openOrReshoot(item.id, 'offOrOn')
        // }
      })
      if (v === 4) {
        this.slidesPerView = this.list.length > 2 ? 2 : 1
      } else {
        this.slidesPerView = this.list.length;
      }
    }
  },
  computed: {
    options() {
      if (this.typeLayout == 4) {
        // this.slidesPerView =this.list.length > 2 ? 2 : 1
        this.slidesPerView = 2;
        this.swiperOption.slidesPerColumn = 0; //每列显示的个数
        this.swiperOption.slidesPerColumnFill = '';
      } else {
        // 每行显示多少个
        this.slidesPerView = this.list.length > 6 || this.list.length === 6 ? Math.ceil(this.list.length / 2) : this.list.length;
        if (this.slidesPerView > 5) {
          this.slidesPerView = 5;
        }
        this.swiperOption.slidesPerColumn = Math.floor(this.list.length / 2); //每列显示的个数
        this.swiperOption.slidesPerColumnFill = 'row';
      }
      return {
        ...this.swiperOption,
        slidesPerView: this.slidesPerView,
      };
    },
  },
  created() {
    window.addEventListener("beforeunload", (e) => this.beforeunloadFn(e));
  },
  destroyed() {
    window.removeEventListener("beforeunload", (e) => this.beforeunloadFn(e));
  },
  mounted() {
    // 创建并保存绑定后的函数引用
    this.boundVisibilityHandler = this.handleVisibilityChange.bind(this);
    // 添加拖拽结束监听
    document.addEventListener('dragend', this.resetDragState);
    // 页面可见性变化处理 先移除旧的监听器（避免重复注册）
    document.removeEventListener('visibilitychange', this.boundVisibilityHandler);
    // 再添加新的监听器
    document.addEventListener('visibilitychange', this.boundVisibilityHandler);
    this.getCarParams();
    if (!this.list.length) {
      this.list = Array(this.type)
        .fill(true)
        .map((it) => ({
          id: Math.random() * 100,
        }));
    }
  },
  activated() {
    this.openWs();
  },
  deactivated() {
    this.closeWs();
    hx_ren = {};
  },
  beforeDestroy() {
    // 使用保存的引用移除
    document.removeEventListener('visibilitychange', this.boundVisibilityHandler);
    document.removeEventListener('dragend', this.resetDragState);
    this.closeWs();
    hx_ren = {};
  },
  // 销毁页面时销毁监
  destroyed() {},
  methods: {
    //获取车场参数
    async getCarParams() {
      let res = await Apis.getCarParams();
      if (res) {
        this.camType = parseInt(res.data.CAM_TYPE);
        this.oldCamType = parseInt(res.data.CAM_TYPE); //保存车场相机类型
        this.camUserName = res.data.CAM_USER;
        this.camPwd = res.data.CAM_PWD;
        this.getChannelList();
      }
    },
    // 打开每秒计时
    openSetInterval(index) {
      this.$set(this.list[index], "openDelay", 4);
      if (!window.MYSetInterval) {
        window.MYSetInterval = [];
      }
      window.MYSetInterval[index] = setInterval(() => {
        // 判断  开闸延时 4000 毫秒
        if (this.list[index].openDelay > 0) {
          this.$set(this.list[index], "openDelay", this.list[index].openDelay - 1);
        } else {
          clearInterval(window.MYSetInterval[index]);
        }
      }, 1000);
    },
    // 关闭退出
    handleCloseTag() {
      this.$Modal.confirm({
        title: "退出",
        content: "确认要退出吗?",
        onOk: () => {
          this.$emit("off");
        },
      });
    },
    // 关闭监控
    closeWs(index = "all") {
      let that = this;
      let item_close = findObjectInArray(this.list, "id", index);
      if (!item_close) return; //元素不存在直接结束
      if (item_close.cameraType !== undefined) {
        that.camType = item_close.cameraType;
      } else {
        that.camType = this.oldCamType;
      }
      if (that.camType == 0) {
        // camera
        if (index === "all") {
          this.list.forEach(item => {
            this.closeWs(item.id)
          })
        } else {
          if (this.ws[index].close) {
            this.ws[index].close(); //关闭视频,不释放底层资源
            this.ws[index].destroy(); //关闭视频，释放底层资源
            this.ws[index] = () => {};
            that.$set(item_close, "play", 0);
          }
        }
      } else if (that.camType == 1) {
        //   华夏相机
        if (index === "all") {
          this.list.forEach(item => {
            this.closeWs(item.id)
          })
        } else {
          if (
            this.ws[index] &&
            Object.keys(this.ws[index].__proto__).indexOf("close") !== -1
          ) {
            this.ws[index].close();
            hx_ren[index] = undefined;
            this.ws[index + "_close"] = true;
            this.ws[index].lockReconnect = true;
            this.ws[index] = () => {};
            that.$set(item_close, "play", 0);
          }
        }
      } else if (that.camType == 2) {
        if (index === "all") {
          this.list.forEach(item => {
            this.closeWs(item.id)
          })
        } else {
          //芊熠相机
          if (this.ws[index]) {
            this.ws[index].destroy();
            this.ws[index] = () => {};
            that.$set(item_close, "play", 0);
            console.log("芊熠销毁")
          }
        }
      } else if (that.camType == 3) {
        //大华相机
        console.log("大华相机关闭视频", index, this.ws[index]);
        if (index === "all") {
          this.list.forEach(item => {
            this.closeWs(item.id)
          })
        } else {
          if (this.ws[index].close) {
            this.ws[index].close(); //关闭视频,不释放底层资源
            this.ws[index].destroy(); //关闭视频，释放底层资源
            this.ws[index] = () => {};
            that.$set(item_close[index], "play", 0);
          }
        }
      } else if (that.camType == 4) { //捷视相机
        //捷视
        if (index === "all") {
          this.list.forEach(item => {
            this.closeWs(item.id)
          })
        } else {
          if (this.ws[index]) {
            if (this.ws[index].unload) {
              this.ws[index].unload();
            }
            this.ws[index].detachMediaElement();
            this.ws[index].destroy();
            this.ws[index] = () => {};
            that.$set(item_close, "play", 0);

          }
        }
      } else if (that.camType == 6) {
        // camera 信路通
        if (index === "all") {
          this.list.forEach(item => {
            this.closeWs(item.id)
          })
        } else {
          if (this.ws[index].destroy) {
            this.ws[index].destroy(); //关闭视频，释放底层资源
            this.ws[index] = () => {};
            that.$set(item_close, "play", 0);
          }
        }
      }
    },
    // 查询全部通道列表
    async getChannelList() {
      let res = await Apis.channelFind(this.searchForm);
      let resStatus = await Apis.channelStatusReport(); // 设备状态
      if (res && this.channelBin && this.channelBin.length > 0) {
        let list = res.data.list.filter((item) =>
          this.channelBin.some((ele) => ele.camera == item.camera && item.channelTypeId)
        );
        length = list.length;
        this.ws = new Array(list.length);
        list.forEach((item, i) => {
          // play = 1 开启视频
          // play = 0 关闭画面
          item.img = "";
          item.play_londing = false;
          item.play = 0;
          item.showBtns = false;
          const index = resStatus.dataList.findIndex(status => item.macAddress === status.mac);
          item.isLine = resStatus.dataList[index].isLine
          // 如果通道没有设置相机  以车场的相机为主
          item.cameraType = item.cameraType == 0 || item.cameraType ? item.cameraType : this.camType;
          this.list.splice(i, 1, item);
        });
        this.applySavedSortOrder();
        await sleep(1000)
        this.openWs();
      } else {
        this.list = [];
      }
    },
    // 监控视频播放
    initVideo(id, url, index) {
      let list_item = findObjectInArray(this.list, "id", index);
      var container = document.getElementById(id)
      this.ws[index] = new window.Jessibuca({
        container: container,
        videoBuffer: 0.2,
        isResize: false,
        isNotMute: false,
        operateBtns: {
          fullscreen: false
        },
        timeout: 10,
        // loadingText: "加载中...",
      })
      // 加载完成 播放视频
      this.ws[index].on("load", () => {
        this.ws[index].play(url);
        this.$set(list_item, "play", 1);
        this.$set(list_item, "play_londing", false);
      })
      // 监听错误
      this.ws[index].on('error', () => {
        console.log("监控error...");
        this.$set(list_item, "play", 0);
        this.$set(list_item, "play_londing", false);
      })
      this.ws[index].on('timeout', function () {})
      // 监听关闭
      this.ws[index].on('close', () => {
        this.$set(list_item, "play", 1);
        this.$set(list_item, "play_londing", false);
      })

    },
    // 打开监控
    async openWs(index = "all") {
      if (index == "all") {
        this.list.forEach(async (item, index) => {
          // openAndOffImg
          // 臻识相机
          if (item.cameraType === 0 || item.cameraType === 5) {
            console.log(index, "臻识", item.ip, item.cameraType);
            this.getZSCamera(item.id);
          } else if (item.cameraType === 1) {
            console.log(index, "华夏", item.ip, item.id)
            this.getHuaKeCamera(item.id);
          } else if (item.cameraType === 4) {
            console.log(index, "视捷", item.ip, item.cameraType, item.id)
            this.getSJCamera(item.id)
          } else if (item.cameraType === 2) {
            console.log(index, "芊熠", item.ip, item.cameraType, item.id)
            this.getQyVideo(item.id)
          } else if (item.cameraType === 6) {
            console.log(item.id, "信路通", item.ip, item.cameraType)
            this.getXLTVideo(item.id);
          }
        })
      } else {
        let that = this;
        // 通过 id 查询数组中对应的项
        let list_item = findObjectInArray(this.list, "id", index);
        if (list_item.cameraType !== undefined) {
          that.camType = list_item.cameraType;
        } else {
          that.camType = this.oldCamType;
        }
        if (that.camType === 0 || that.camType === 5) {
          // console.log(index, "臻识", item.ip);
          this.getZSCamera(list_item.id);
        } else if (that.camType === 1) {
          // console.log(index, "华夏", item.ip)
          this.getHuaKeCamera(list_item.id);
        } else if (that.camType === 4) {
          // console.log(index, "视捷", item.ip)
          this.getSJCamera(list_item.id)
        } else if (that.camType === 2) {
          console.log(index, "芊熠", list_item.ip)
          this.getQyVideo(list_item.id)
        } else if (that.camType === 6) {
          console.log(index, "信路通", list_item.ip)
          this.getXLTVideo(index);
        }
      }
    },
    // 臻识相机视频请求
    async getZSCamera(index) {
      let list_item = findObjectInArray(this.list, "id", index);
      //  臻识相机
      this.$set(list_item, "play_londing", true);
      let ip = list_item.camera;
      var port = 80; // 设备网页端口
      var proto = "http"; // 协议，http 或 https
      var username = "admin"; // 设备网页登录用户名
      var password = "admin"; // 设备网页登录密码
      var data = JSON.stringify({
        "type": "get_live_stream_type",
        "module": "BUS_WEB_REQUEST"
      });
      let url = `http://${ip}:${port}/request.php`;
      // 2. 创建链接  ---> 设置请求方式 和 url
      const xhr = new XMLHttpRequest();
      xhr.open("POST", url);
      xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
      xhr.send(data);
      xhr.onreadystatechange = async () => {
        // 对象解构
        const {
          readyState,
          status
        } = xhr
        if (readyState != 4) return;
        // 判断响应状态码
        if ((status >= 200 && status < 300) || status === 304) {
          let data = JSON.parse(xhr.responseText);
          var video_port = data.body.port
          var pubkey = Base64.decode(data.body.pubkey)
          let encryptStr = new JSEncrypt()
          encryptStr.setPublicKey(pubkey)
          var str = username + ':' + password
          var token = encodeURIComponent(encryptStr.encrypt(str.toString()))
          var video_url = "ws://" + ip + ":" + video_port + "/ws.flv?token=" + token;
          if (video_url) {
            await sleep(1000)
            this.$nextTick(() => {
              this.initVideo(`img__v${index}`, video_url, index) // 播放臻识视频
            })
          }
        } else {
          this.$set(list_item, "play_londing", false);
        }
      }
    },
    // 华夏相机请求
    getHuaKeCamera(index) {
      this.playHxVideo(index)
    },
    // 初始化华夏相机视频流
    playHxVideo(index) {
      let list_item = findObjectInArray(this.list, "id", index);
      this.$set(list_item, "play_londing", true);
      // hx_ren[index].imgLabel = null;
      // 清理现有连接
      if (this.ws[index] && this.ws[index].close && this.ws[index].readyState != WebSocket.CLOSED) {
        this.ws[index].close();
        // hx_ren[index] = undefined;
      }

      const ip = list_item.camera;
      if (!ip) {
        console.error("Camera IP is missing");
        this.updateCameraStatus(index, 0, false);
        return;
      }

      try {
        this.ws[index] = new WebSocket(`ws://${ip}:9999`);
        // 控制视频关闭
        if (this.ws[index + "_close"] == undefined) {
          this.ws[index + "_close"] = false;
        }
        if (!hx_ren[index]) {
          hx_ren[index] = {
            renderer: new Renderer(),
            imgLabel: document.getElementById(`img__${index}`)
          };
        } else {
          if (!hx_ren[index].imgLabel) {
            hx_ren[index].imgLabel = null;
            hx_ren[index].renderer = null;
            hx_ren[index].imgLabel = document.getElementById(`img__${index}`)
          }
        }
        this.setupWebSocketHandlers(index);
      } catch (error) {
        console.error("WebSocket creation error:", error);
        this.scheduleReconnect(index);
      }
    },
    // 设置WebSocket处理器
    setupWebSocketHandlers(index) {
      let list_item = findObjectInArray(this.list, "id", index);
      const ws = this.ws[index];
      const that = this;
      ws.onopen = function (event) {
        console.log("WebSocket connected");
        that.updateCameraStatus(index, 1, false);
      };
      ws.onmessage = (event) => {
        this.handleWebSocketMessage(index, event);
      };

      ws.onclose = function (event) {
        console.error("WebSocket closed");
        if (list_item.isLine) {
          if (!that.ws[index + "_close"]) {
            that.scheduleReconnect(index);
          }
        } else {
          that.updateCameraStatus(index, 0, false);
        }
      };
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
        if (list_item.isLine) {
          if (!this.ws[index + "_close"]) {
            that.scheduleReconnect(index);
          }
        } else {
          that.updateCameraStatus(index, 0, false);
        }
      };
    },

    // 处理WebSocket消息
    handleWebSocketMessage(index, event) {
      if (!hx_ren[index].imgLabel) {
        hx_ren[index].imgLabel = document.getElementById(`img__${index}`)
      }
      hx_ren[index].renderer.render(event.data, hx_ren[index].imgLabel)
    },
    // 计划重连
    scheduleReconnect(index) {
      if (!this.ws[index] || this.ws[index].lockReconnect || this.ws[index + "_close"]) return;
      this.ws[index].lockReconnect = true;
      setTimeout(() => {
        console.log('Attempting reconnect');
        this.playHxVideo(index);
        this.ws[index].lockReconnect = false;
      }, 2000);
    },

    // 更新相机状态
    updateCameraStatus(index, playState, loadingState) {
      let list_item = findObjectInArray(this.list, "id", index);
      this.$set(list_item, "play", playState);
      this.$set(list_item, "play_londing", loadingState);
    },
    // 芊熠相机的请求 
    async getQyVideo(index) {
      let list_item = findObjectInArray(this.list, "id", index);
      let that = this;
      //芊熠相机
      const BASE_URL = process.env.NODE_ENV === "production";
      let host = BASE_URL ? location.hostname : "************";
      let port = BASE_URL ? "8083" : "8083";
      let ip = list_item.camera;
      this.$set(list_item, "play_londing", true);
      this.ws[index] = new Rtsp2WPlayer({
        //RTSPtoWebRTC服务器地址
        serverPath: `http://${host}:${port}`,
        //视频容器ID
        containerId: `video__qy${index}`,
        //是否在控制台输出日志
        logEnabled: false,
        //视频控件样式：HTML元素video的样式
        style: {
          controls: false,
        },
        // state 连接状态--- connecting:正在连接;connected:连接成功;disconnected:连接断开;failed:连接失败;
        onconnectionstatechange: (state) => {
          // //如果连接断开或连接失败，则5秒后重新play
          if (state === "connected") {
            console.log("连接成功");
            that.$set(list_item, "play_londing", false);
            that.$set(list_item, "play", 1);
          }
          // 以防容器中出现多个视频监控画面
          var containers = document.querySelectorAll(`#video__qy${index}`);
          containers.forEach(function (container) {
            // 获取容器内的所有 video 子元素
            var videos = container.querySelectorAll('video');
            // 从第二个 video 元素开始遍历（索引为 1）
            for (var i = videos.length - 1; i > 0; i--) {
              // 删除除了第一个之外的所有 video 元素
              container.removeChild(videos[i]);
            }
          });
          if ("disconnected" === state || "failed" === state) {
            that.$set(list_item, "play", 0);
            setTimeout(() => {
              console.log("正在重连...");
              this.playQyVideo(`rtsp://${ip}:50000/video`, index);
            }, 5000);
          }
        },
      });
      await sleep(100);
      this.playQyVideo(`rtsp://${ip}:50000/video`, index);
      setTimeout(() => {
        this.$set(list_item, "play_londing", false);
      }, 10000);
    },

    // 视捷相机请求
    async getSJCamera(index) {
      let that = this;
      let list_item = findObjectInArray(this.list, "id", index);
      await sleep(500);
      this.$set(list_item, "play_londing", true);
      let ip = list_item.camera;
      var videoElement = document.getElementById(`sjliveVideo_${index}`);
      this.ws[index] = mpegts.createPlayer({
        type: "flv", // could also be mpegts, m2ts, flv
        hasVideo: true,
        hasAudio: false,
        isLive: true,
        url: "ws://" + ip + ":" + 8081 + "/live_video0.flv",
      }, {
        enableStashBuffer: false, // 如果您需要实时（最小延迟）来进行实时流播放，则设置为false 用缓冲区，减少延迟
        autoCleanupSourceBuffer: true, // 对SourceBuffer进行自动清理【自动清理内存】
        stashInitialSize: 128, // 减少首帧显示等待时长
        enableWorker: true, // 启用分离的线程进行转换
        isLive: true,
        liveBufferLatencyChasingOnPaused: true,
        liveBufferLatencyChasing: true,
        liveBufferLatencyMaxLatency: 0.80,
        liveBufferLatencyMinRemain: 0.20,
        liveSync: false, // 启用动态同步
        liveSyncMaxLatency: 0.80,
        liveSyncTargetLatency: 0.20, // 目标延迟 0.05 秒
        liveSyncPlaybackRate: 1.5, //追赶时的播放速率 1.5倍数
      });
      const listener_error = async (error) => {
        console.error('视捷播放错误:', error);
        // 检测是否是网络错误或断流
        if (error.type === 'NetworkError' || error.detail === 'fetch异常') {
          // 触发重连逻辑
          if (list_item.isLine) {
            console.log('尝试重新连接...');
            // 先销毁旧的播放器实例
            this.ws[index].destroy();
            // 延迟 3 秒后重新创建播放器（避免频繁重试）
            setTimeout(() => {
              this.ws[index].attachMediaElement(videoElement); // videoElement 是 <video> 元素
              this.ws[index].load(); // 重新加载流
              this.ws[index].play().catch(e => console.error('自动播放失败:', e));
            }, 3000);
          } else {
            this.ws[index].pause();
            this.ws[index].unload();
            this.ws[index].detachMediaElement();
            this.ws[index].destroy();
            that.updateCameraStatus(index, 0, false);
          }
        }
      }
      const listener_loading_complete = async () => {
        // 视频断流后
        if (!list_item.isLine) {
          this.ws[index].pause();
          this.ws[index].unload();
          this.ws[index].detachMediaElement();
          this.ws[index].destroy();
          that.updateCameraStatus(index, 0, false);
        }
      }
      this.ws[index].on(mpegts.Events.LOADING_COMPLETE, listener_loading_complete);
      this.ws[index].on(mpegts.Events.ERROR, listener_error);
      this.ws[index].attachMediaElement(videoElement);
      this.ws[index].load();
      this.ws[index].play();
      that.$set(list_item, "play", 1);
      that.$set(list_item, "play_londing", false);

    },
    playQyVideo(rtspUrl, index) {
      this.ws[index].play(rtspUrl);
    },
    // 信路通的请求
    async getXLTVideo(index) {
      let list_item = findObjectInArray(this.list, "id", index);
      let that = this;
      // console.log(list_item,"信路通的请求")
      this.$set(list_item, "play_londing", false);
      let ip = list_item.camera;
      let wsUrl = `ws://${ip}:9950`; // 播放URL sjliveVideo_
      await sleep(500)
      let video = document.getElementById(`xltVideo_${index}`);
      // 初始化播放器
      this.ws[index] = new Wfs();
      // 监听接收信息
      this.ws[index].websocketLoader.wsOnmessage = (wfs, event) => {
        // console.log(event.data) // 接受的数据额外处理
      };
      // ws打开
      this.ws[index].websocketLoader.wsOpen = (wfs, event) => {
        console.log('开启') // 接受的数据额外处理
        this.$set(list_item, "play", 1);
        this.$set(list_item, "play_londing", false);
      };
      this.ws[index].websocketLoader.wsOnClose = (wfs, event) => {
        console.log('关闭') // 接受的数据额外处理
      };
      this.$nextTick(() => {
        // 播放地址
        this.ws[index].config.websocketUri = wsUrl;
        console.log(this.ws[index].config.websocketUri)
        this.ws[index].config.parameter = "h264_protocol"; //h264_protocol  第一路  h264-second_protocol第二路
        // 开始播放
        this.ws[index].attachMedia(video);
      })
      // 加载等待超时秒钟后结束
      setTimeout(() => {
        if (list_item.play_londing) {
          that.$set(list_item, "play_londing", false);
        }
      }, 9000);
    },
    //	0 开闸  3关闸  1 主相机补拍  offOrOn 打开或关闭 ws
    // 补拍和开闸事件
    async openOrReshoot(index, it, type, loadingNum) {
      if (it === "offOrOn") {
        console.log("index1", index);
        return this.openAndOffImg(index);
      }
      this.$store.commit("loadingList", +(loadingNum + index));
      let res = await Apis.openOrReshoot("", "non", `${it.macAddress}`);
      res && this.$Message.success(res.msg);
      res && type === "0" && this.openSetInterval(index);
    },
    // 关闭或打开相机
    openAndOffImg(index) {
      console.log("index2", index);
      let list_item = findObjectInArray(this.list, "id", index);
      if (list_item && list_item.play) {
        this.closeWs(index);
      } else {
        this.openWs(index);
      }
    },
    closeAllVideo() {
      for (let i = 0; i < this.ws.length; i++) {
        this.closeWs(i);
        this.$emit("closeVideo");
      }
    },
    beforeunloadFn(e) {
      console.log("刷新或关闭");
      this.closeWs();
      hx_ren = {};
    },
    // 视捷全屏
    screenbtnFn(index) {
      let videoElement = document.getElementById(`sjliveVideo_${index}`);
      videoElement.webkitRequestFullScreen();
    },
    // 鼠标移入显示按钮
    showButtons(index, channelName) {
      this.list[index].showBtns = true;
    },
    hiddenButton(index) {
      this.list[index].showBtns = false;
    },
    handleVisibilityChange() {
      if (document.visibilityState === 'hidden') {
        this.list.forEach(item => {
          if (item.play && item.cameraType == 4) {
            this.closeWs(item.id)
          }
        })
      }
      if (document.visibilityState === 'visible') {
        this.list.forEach(async (item) => {
          if (item.cameraType == 4) {
            await sleep(1000)
            this.openWs(item.id)
          }
        })
      }
    }
  },
};