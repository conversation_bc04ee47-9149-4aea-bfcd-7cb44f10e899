<template>
  <div class="simple-camera">
    <div class="camera-header">
      <span class="camera-title">{{ channelName }}</span>
      <span class="connection-status" :class="statusClass">
        {{ statusText }}
      </span>
    </div>
    
    <div class="camera-content">
      <!-- 臻识相机使用Jessibuca播放器 -->
      <div v-if="props.cameraPort === 9080" class="jessibuca-container">
        <div :id="`jessibuca-${props.cameraId}`" class="jessibuca-player"></div>
        <div v-if="!isConnected" class="camera-placeholder overlay">
          <div class="placeholder-icon">📷</div>
          <!-- <div class="placeholder-text">{{ placeholderText }}</div>
          <div class="camera-info">{{ cameraIp }}:{{ cameraPort }}</div>
          <div class="demo-note">
            <div class="camera-type">臻识相机 (Jessibuca播放器)</div>
            <div class="demo-status">演示环境 - 摄像头服务器可能离线</div> -->
          <!-- </div> -->
        </div>
      </div>

      <!-- 华夏相机使用WebSocket + 图片显示 -->
      <div v-else class="huaxia-container">
        <div v-if="isConnected && imageUrl" class="camera-image">
          <img :src="imageUrl" alt="摄像头画面" />
        </div>
        <div v-else class="camera-placeholder">
          <div class="placeholder-icon">📷</div>
          <!-- <div class="placeholder-text">{{ placeholderText }}</div>
          <div class="camera-info">{{ cameraIp }}:{{ cameraPort }}</div>
          <div class="demo-note">
            <div class="camera-type">华夏相机 (WebSocket直连)</div>
            <div class="demo-status">演示环境 - 摄像头服务器可能离线</div> -->
          <!-- </div> -->
        </div>
      </div>
    </div>
    
    <div class="camera-controls">
      <button
        @click="toggleConnection"
        :class="['control-btn', isConnected ? 'btn-disconnect' : 'btn-connect']"
        :disabled="isConnecting"
        :title="`点击${isConnected ? '断开' : '连接'}摄像头 (${props.cameraId})`"
      >
        {{ isConnected ? '断开' : '连接' }}
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { cameraConfigApi } from '../api/cameraConfigApi'
import JSEncrypt from 'jsencrypt'

// Props
interface Props {
  cameraId: string
  cameraIp: string
  cameraPort: number
  channelName: string
  autoConnect?: boolean
  token?: string
}

const props = withDefaults(defineProps<Props>(), {
  autoConnect: false,
  token: ''
})

// 响应式数据
const isConnected = ref(false)
const isConnecting = ref(false)
const imageUrl = ref('')
const lastError = ref('')
const lastErrorTime = ref(0)
const dataStats = ref({
  totalPackets: 0,
  imagePackets: 0,
  controlPackets: 0,
  lastImageSize: 0,
  lastImageTime: 0
})
let ws: WebSocket | null = null
let jessibucaPlayer: any = null
let reconnectAttempts = 0

// 计算属性
const statusClass = computed(() => {
  if (isConnecting.value) return 'connecting'
  if (isConnected.value) return 'connected'
  return 'disconnected'
})

const statusText = computed(() => {
  if (isConnecting.value) return '连接中...'
  if (isConnected.value) return '在线'
  return '离线'
})

const placeholderText = computed(() => {
  const cameraType = props.cameraPort === 9080 ? '臻识相机' : '华夏相机'

  if (isConnecting.value) return '正在连接摄像头...'
  if (lastError.value) {
    if (lastError.value === '服务器不可用' || lastError.value === '连接被拒绝或网络不可达') {
      return `${cameraType}离线 (演示环境)`
    }
    return `连接失败: ${lastError.value}`
  }
  return `${cameraType}待连接`
})

// WebSocket URL
const wsUrl = computed(() => {
  // 根据端口判断摄像头类型和URL格式
  if (props.cameraPort === 9080) {
    // 臻识相机 - 需要通过API获取token，这里先用简化版本
    return `ws://${props.cameraIp}:${props.cameraPort}/ws.flv`
  } else if (props.cameraPort === 9999) {
    // 华夏相机 - 直接WebSocket连接
    return `ws://${props.cameraIp}:${props.cameraPort}/`
  } else {
    // 默认格式
    return `ws://${props.cameraIp}:${props.cameraPort}/`
  }
})

// 获取臻识相机的RSA加密token
const getZhenshiToken = async (ip: string, username: string, password: string): Promise<string> => {
  try {
    // 第一步：调用API获取公钥
    const response = await fetch(`http://${ip}:80/request.php`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: JSON.stringify({
        "type": "get_live_stream_type",
        "module": "BUS_WEB_REQUEST"
      })
    })

    if (!response.ok) {
      throw new Error('获取公钥失败')
    }

    const data = await response.json()
    // const videoPort = data.body.port // 视频端口，这里不需要使用
    const pubkey = atob(data.body.pubkey) // Base64解码公钥

    // 第二步：使用JSEncrypt进行RSA加密
    const encrypt = new JSEncrypt()
    encrypt.setPublicKey(pubkey)

    const str = `${username}:${password}`
    const encryptedStr = encrypt.encrypt(str)

    if (!encryptedStr) {
      throw new Error('RSA加密失败')
    }

    // 第三步：URL编码
    const token = encodeURIComponent(encryptedStr)

    return token

  } catch (error) {
    console.error('🎥 获取臻识相机token失败:', error)
    throw error
  }
}

// 获取实际的WebSocket URL（包括token处理）
const getActualWebSocketUrl = async (): Promise<string> => {
  if (props.cameraPort === 9080) {
    // 臻识相机 - 需要获取RSA加密token
    try {
      const token = await getZhenshiToken(props.cameraIp, 'admin', 'admin')
      return `ws://${props.cameraIp}:${props.cameraPort}/ws.flv?token=${token}`
    } catch (error) {
      console.error('🎥 获取臻识相机token失败:', error)
      // 回退到无token版本
      return `ws://${props.cameraIp}:${props.cameraPort}/ws.flv`
    }
  } else {
    // 华夏相机或其他 - 直接连接
    return wsUrl.value
  }
}

// 初始化Jessibuca播放器（臻识相机）
const initJessibucaPlayer = async () => {
  try {
    const actualUrl = await getActualWebSocketUrl()

    const container = document.getElementById(`jessibuca-${props.cameraId}`)
    if (!container) {
      throw new Error('找不到Jessibuca容器')
    }

    // 检查Jessibuca是否可用
    if (!window.Jessibuca) {
      console.error('🎥 Jessibuca播放器未加载，请检查CDN链接')
      throw new Error('Jessibuca播放器未加载')
    }

    // Jessibuca播放器已加载

    // 如果已经有播放器实例，先销毁它
    if (jessibucaPlayer) {
      try {
        jessibucaPlayer.destroy()
      } catch (e) {
        console.warn('🎥 销毁播放器时出错:', e)
      }
      jessibucaPlayer = null

      // 等待一小段时间确保销毁完成
      await new Promise(resolve => setTimeout(resolve, 100))
    }

    // 清空容器内容并重新创建
    container.innerHTML = ''

    // 确保容器有正确的ID
    if (!container.id) {
      container.id = `jessibuca-${props.cameraId}`
    }

    jessibucaPlayer = new window.Jessibuca({
      container: container,
      videoBuffer: 0.2,
      isResize: false,
      isNotMute: false,
      operateBtns: {
        fullscreen: false
      },
      timeout: 10,
      loadingText: "加载中...",
      decoder: '/jessibuca/decoder.js',
      wasmDecoder: '/jessibuca/decoder.wasm'
    })

    // 监听加载完成事件
    jessibucaPlayer.on("load", () => {
      jessibucaPlayer.play(actualUrl)
      isConnected.value = true
      isConnecting.value = false
      reconnectAttempts = 0
    })

    // 监听错误事件
    jessibucaPlayer.on('error', (error: any) => {
      console.error('🎥 Jessibuca播放错误:', error)
      lastError.value = '播放失败'
      isConnected.value = false
      isConnecting.value = false
    })

    // 监听超时事件
    jessibucaPlayer.on('timeout', () => {
      console.warn('🎥 Jessibuca播放超时')
      lastError.value = '播放超时'
      isConnected.value = false
      isConnecting.value = false
    })

    // 监听关闭事件
    jessibucaPlayer.on('close', () => {
      isConnected.value = false
      isConnecting.value = false
    })

  } catch (error) {
    console.error('🎥 初始化Jessibuca播放器失败:', error)
    lastError.value = '播放器初始化失败'
    isConnecting.value = false
  }
}

// 连接摄像头
const connect = async () => {
  if (isConnecting.value || isConnected.value) return

  // 开始连接摄像头

  isConnecting.value = true
  lastError.value = ''

  // 臻识相机使用Jessibuca播放器
  if (props.cameraPort === 9080) {
    await initJessibucaPlayer()
    return
  }

  // 华夏相机使用WebSocket
  try {
    // 获取实际的WebSocket URL
    const actualUrl = await getActualWebSocketUrl()

    ws = new WebSocket(actualUrl)

    // 设置连接超时
    const connectTimeout = setTimeout(() => {
      if (ws && ws.readyState === WebSocket.CONNECTING) {
        console.warn('🎥 摄像头连接超时:', props.cameraId)
        ws.close()
        lastError.value = '连接超时'
        isConnecting.value = false
      }
    }, 10000) // 10秒超时

    ws.onopen = () => {
      clearTimeout(connectTimeout)
      isConnected.value = true
      isConnecting.value = false
      lastError.value = ''
    }

    ws.onmessage = (event) => {
      // 处理图像数据
      if (event.data instanceof Blob) {
        dataStats.value.totalPackets++

        // 只处理大于1KB的数据（真实图片数据）
        if (event.data.size > 1024) {
          dataStats.value.imagePackets++
          dataStats.value.lastImageSize = event.data.size
          dataStats.value.lastImageTime = Date.now()

          // 图片数据接收成功

          // 清理之前的URL
          if (imageUrl.value) {
            URL.revokeObjectURL(imageUrl.value)
          }

          // 创建新的图像URL
          imageUrl.value = URL.createObjectURL(event.data)
        } else {
          // 小数据包，可能是心跳或控制信息
          dataStats.value.controlPackets++
        }
      }
    }

    ws.onerror = (error) => {
      clearTimeout(connectTimeout)
      const now = Date.now()
      // 避免频繁显示相同错误（5秒内只显示一次）
      if (now - lastErrorTime.value > 5000) {
        console.warn('🎥 摄像头连接失败:', props.cameraId, '- 服务器可能不可用')
        lastErrorTime.value = now
      }
      lastError.value = '服务器不可用'
      isConnected.value = false
      isConnecting.value = false
      reconnectAttempts++
    }

    ws.onclose = (event) => {
      clearTimeout(connectTimeout)

      isConnected.value = false
      isConnecting.value = false

      // 清理图像URL
      if (imageUrl.value) {
        URL.revokeObjectURL(imageUrl.value)
        imageUrl.value = ''
      }

      // 根据关闭代码设置错误信息
      if (event.code !== 1000 && event.code !== 1001) {
        if (event.code === 1006) {
          lastError.value = '连接被拒绝或网络不可达'
        } else {
          lastError.value = '连接意外断开'
        }
      }
    }

  } catch (error) {
    console.error('🎥 创建WebSocket失败:', error)
    lastError.value = '创建连接失败'
    isConnecting.value = false
  }
}

// 断开连接
const disconnect = () => {
  // 清理WebSocket连接（华夏相机）
  if (ws) {
    ws.close()
    ws = null
  }

  // 清理Jessibuca播放器（臻识相机）
  if (jessibucaPlayer) {
    try {
      jessibucaPlayer.destroy()
      jessibucaPlayer = null
    } catch (error) {
      console.error('🎥 销毁Jessibuca播放器失败:', error)
    }
  }

  isConnected.value = false
  isConnecting.value = false

  // 清理图像URL
  if (imageUrl.value) {
    URL.revokeObjectURL(imageUrl.value)
    imageUrl.value = ''
  }

  // 重置统计信息
  dataStats.value = {
    totalPackets: 0,
    imagePackets: 0,
    controlPackets: 0,
    lastImageSize: 0,
    lastImageTime: 0
  }
}

// 切换连接状态
const toggleConnection = () => {

  if (isConnected.value) {
    disconnect()
  } else {
    connect()
  }
}

// 检查Jessibuca是否加载
const checkJessibucaAvailability = () => {
  // 如果Jessibuca未加载，等待一段时间后重试
  if (!window.Jessibuca) {
    setTimeout(() => {
      checkJessibucaAvailability()
    }, 1000)
  }
}

// 生命周期
onMounted(() => {
  // 检查Jessibuca可用性
  checkJessibucaAvailability()

  if (props.autoConnect) {
    connect()
  }
})

onUnmounted(() => {
  disconnect()
})

// 暴露方法
defineExpose({
  connect,
  disconnect,
  isConnected: computed(() => isConnected.value)
})
</script>

<script lang="ts">
export default {
  name: 'SimpleCameraDisplay'
}
</script>

<style scoped>
.simple-camera {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  background: #000;
  border-radius: 0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
  box-sizing: border-box;
}

.camera-header {
  background: rgba(0, 0, 0, 0.8);
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  font-size: 14px;
}

.camera-title {
  font-weight: bold;
}

.connection-status {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
}

.connection-status.connected {
  background: #4caf50;
  color: white;
}

.connection-status.connecting {
  background: #ff9800;
  color: white;
}

.connection-status.disconnected {
  background: #f44336;
  color: white;
}

.camera-content {
  flex: 1;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  width: 100%;
  max-width: 100%;
}

.camera-image {
  flex: 1;
  width: 100%;
}

.camera-image img {
  width: 100%;
  height: 100%;
  max-width: 100%;
  max-height: 100%;
  object-fit: contain; /* 保持比例，不裁剪 */
  border-radius: 0; /* 直角显示 */
}

.jessibuca-container {
  width: 100%;
  height: 100%;
  position: relative;
}

.jessibuca-player {
  width: 100%;
  height: 100%;
  border-radius: 0; /* 直角显示 */
}

.huaxia-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.camera-placeholder {
  text-align: center;
  color: #888;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.camera-placeholder.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.placeholder-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.placeholder-text {
  font-size: 16px;
  margin-bottom: 8px;
}

.camera-info {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.demo-note {
  font-size: 10px;
  color: #999;
  font-style: italic;
}

.camera-controls {
  padding: 8px 12px;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 999;
  min-height: 40px;
}

.control-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s;
}

.btn-connect {
  background: #4caf50;
  color: white;
}

.btn-disconnect {
  background: #f44336;
  color: white;
}

.btn-connect:hover {
  background: #388e3c;
}

.btn-disconnect:hover {
  background: #d32f2f;
}

.control-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.url-info {
  color: #ccc;
  font-size: 10px;
  font-family: monospace;
}


</style>
