<template>
  <div class="exit-control-panel">
    <!-- 标题栏 -->
    <div class="panel-header">
      <span class="panel-title">出场管理</span>
      <span class="channel-name">电单车出口</span>
    </div>

    <!-- 车牌号区域 -->
    <div class="plate-section">
      <div class="plate-display">
        <span class="plate-prefix">空</span>
        <NInput
          v-model="editablePlateNumber"
          class="plate-input"
          placeholder="车牌号码"
          :bordered="false"
          :show-count="false"
        />
      </div>
      <NButton type="primary" size="small" class="confirm-btn" @click="handleConfirmPlateNumber">
        确定修改
      </NButton>
    </div>

    <!-- 快捷操作按钮 -->
    <div class="quick-actions">
      <NButton type="success" size="small" class="quick-btn">入场匹配</NButton>
      <NButton type="primary" size="small" class="quick-btn">重新抓拍</NButton>
      <NButton type="info" size="small" class="quick-btn">入场小票</NButton>
      <NButton type="error" size="small" class="quick-btn" @click="handleForceRelease">强制放行</NButton>
    </div>

    <!-- 车辆信息 -->
    <div class="vehicle-info">
      <div class="info-row">
        <span class="info-label">卡号</span>
      </div>

      <div class="info-row">
        <span class="info-label">通道类型</span>
        <NSelect
          v-model="selectedVehicleType"
          :options="vehicleTypeOptions"
          size="small"
          class="vehicle-select"
        />
      </div>

      <div class="info-row">
        <span class="info-label">车辆类型</span>
        <span class="info-value">临时车</span>
      </div>

      <div class="info-row">
        <span class="info-label">入场时间</span>
        <span class="info-value">2025-07-21 17:48:52</span>
      </div>

      <div class="info-row">
        <span class="info-label">停车时长</span>
        <span class="info-value">1天</span>
      </div>

      <div class="info-row">
        <span class="info-label">提前缴扣</span>
        <span class="info-value">0MOP</span>
      </div>

      <div class="info-row">
        <span class="info-label">当前欠费</span>
        <span class="info-value">0MOP</span>
      </div>
    </div>

    <!-- 应缴金额区域 -->
    <div class="fee-section">
      <div class="fee-display">
        <span class="fee-label">应缴金额</span>
        <span class="fee-amount">3.9MOP</span>
        <NButton type="success" size="small" class="detail-btn" @click="handleBillingDetails">
          计费详情
        </NButton>
      </div>
    </div>

    <!-- 支付按钮 -->
    <div class="payment-buttons">
      <div class="payment-row">
        <NButton
          disabled
          class="payment-btn coupon-btn"
          @click="handleCouponPayment"
        >
          <span class="btn-icon">🎫</span>
          抵用券
        </NButton>
        <NButton
          type="success"
          class="payment-btn free-btn"
          @click="handleFreePass"
        >
          免费放行
        </NButton>
      </div>
      <NButton
        type="primary"
        class="payment-btn cash-btn"
        @click="handleCashPayment"
      >
        现金收费
      </NButton>
    </div>

    <!-- 免费放行弹框 -->
    <FreePassModal
      :visible="showFreePassModal"
      @update:visible="showFreePassModal = $event"
      @confirm="handleFreePassConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { NButton, NSelect, NInput, useMessage } from 'naive-ui'
import FreePassModal from '../../views/model/FreePassModal/index.vue'
import type { MonitorData } from '../../data/mockMonitorData'

defineOptions({
  name: 'ExitControlPanel'
})

// Props
interface Props {
  selectedMonitorData?: MonitorData | null
}

const props = withDefaults(defineProps<Props>(), {
  selectedMonitorData: null
})

// Emits
interface Emits {
  (e: 'normal-exit'): void
  (e: 'manual-exit'): void
  (e: 'exception-exit'): void
  (e: 'view-details'): void
  (e: 'open-gate'): void
  (e: 'refresh-data'): void
  (e: 'billing-details'): void
}

const emit = defineEmits<Emits>()

// Message 实例
const message = useMessage()

// 车辆类型选择
const selectedVehicleType = ref('电单车')
const vehicleTypeOptions = [
  { label: '电单车', value: '电单车' },
  { label: '私家车', value: '私家车' },
  { label: '货车', value: '货车' },
  { label: '其他', value: '其他' }
]

// 可编辑车牌号
const editablePlateNumber = ref('')

// 免费放行弹框
const showFreePassModal = ref(false)

// 确认修改车牌号
const handleConfirmPlateNumber = () => {
  if (editablePlateNumber.value.trim()) {
    // 这里可以触发一个事件通知父组件车牌号已修改
    console.log('车牌号已修改为:', editablePlateNumber.value)
    // 可以添加成功提示
  }
}

// 支付相关事件处理
const handleCouponPayment = () => {
  console.log('抵用券支付')
  // 抵用券支付逻辑
}

const handleFreePass = () => {
  console.log('打开免费放行弹框')
  showFreePassModal.value = true
}

const handleFreePassConfirm = (data: { type: string; reason: string }) => {
  console.log('免费放行确认:', data)
  showFreePassModal.value = false

  // 显示成功提示
  message.success(`免费放行成功 - ${data.type === 'leader' ? '领导审批' : '内部放行'}`, {
    duration: 3000
  })

  // 触发正常出场事件
  emit('normal-exit')
}

const handleCashPayment = () => {
  console.log('现金收费')
  emit('normal-exit')
}

// 计费详情
const handleBillingDetails = () => {
  console.log('查看计费详情')
  emit('billing-details')
}

// 强制放行
const handleForceRelease = () => {
  console.log('强制放行')
  message.success('强制放行成功！车辆已允许通行', {
    duration: 3000
  })
  // 可以触发相关事件
  emit('normal-exit')
}

// 监听选中的监控数据变化
watch(() => props.selectedMonitorData, (newData) => {
  if (newData) {
    editablePlateNumber.value = newData.plateNumber
  }
})
</script>

<style scoped>
.exit-control-panel {
  width: 400px;
  height: auto;
  max-height: calc(100vh - 60px);
  background: #f5f5f5;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow-y: auto;
}

/* 标题栏 */
.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.panel-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.channel-name {
  font-size: 14px;
  color: #ff4d4f;
  font-weight: 500;
}

/* 车牌号区域 */
.plate-section {
  padding: 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.plate-display {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  background: #f8f9fa;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}

.plate-prefix {
  background: #52c41a;
  color: white;
  padding: 8px 12px;
  font-weight: bold;
  font-size: 16px;
  min-width: 40px;
  text-align: center;
}

.plate-input {
  flex: 1;
  background: #fff;
}

.plate-input :deep(.n-input__input-el) {
  padding: 8px 12px;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  border: none;
  background: transparent;
}

.confirm-btn {
  width: 100%;
  height: 32px;
  font-size: 14px;
}

/* 快捷操作按钮 */
.quick-actions {
  padding: 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
}

.quick-btn {
  height: 32px;
  font-size: 12px;
}

/* 车辆信息 */
.vehicle-info {
  padding: 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.info-row:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666;
  min-width: 70px;
}

.info-value {
  font-size: 14px;
  color: #333;
  text-align: right;
}

.vehicle-select {
  width: 80px;
}

/* 应缴金额区域 */
.fee-section {
  padding: 20px;
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
}

.fee-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.fee-label {
  font-size: 14px;
  color: #666;
}

.fee-amount {
  font-size: 24px;
  font-weight: bold;
  color: #ff4d4f;
}

.detail-btn {
  height: 28px;
  font-size: 12px;
}

.fee-status {
  font-size: 14px;
  color: #ff4d4f;
  text-align: center;
}

/* 支付按钮 */
.payment-buttons {
  padding: 20px;
  background: #fff;
}

.payment-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 12px;
}

.payment-btn {
  height: 48px;
  font-size: 15px;
  font-weight: 600;
  border-radius: 8px;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.payment-btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.payment-btn:active:not(:disabled) {
  transform: translateY(0);
}

.btn-icon {
  font-size: 16px;
}

/* 抵用券按钮 - 禁用状态 */
.coupon-btn {
  background: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #bfbfbf !important;
  cursor: not-allowed !important;
}

.coupon-btn .btn-icon {
  opacity: 0.5;
}

/* 免费放行按钮 - 绿色 */
.free-btn {
  background: linear-gradient(135deg, #52c41a, #73d13d) !important;
  border-color: #52c41a !important;
  color: white !important;
}

.free-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #73d13d, #95de64) !important;
}

/* 现金收费按钮 - 蓝色 */
.cash-btn {
  background: linear-gradient(135deg, #1890ff, #40a9ff) !important;
  border-color: #1890ff !important;
  color: white !important;
  width: 100%;
}

.cash-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #40a9ff, #69c0ff) !important;
}
</style>
