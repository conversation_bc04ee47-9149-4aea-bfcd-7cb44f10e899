# 岗亭切换功能测试文档

## 功能概述

本文档描述了停车收费系统中的岗亭切换功能，用户可以在出口岗亭和中央岗亭之间进行切换。

## 🎯 功能特点

### 1. 岗亭切换按钮
- **位置**: 顶部菜单栏左侧
- **样式**: 按钮组形式，包含两个按钮
- **状态**: 当前激活的岗亭按钮显示为主色调

### 2. 出口岗亭 (默认页面)
- **路由**: `/` (根路径)
- **标题**: 出口岗亭
- **功能**: 
  - 入口/出口监控
  - 车牌识别和输入
  - 车辆放行控制
  - 收费管理
  - 各种业务弹框

### 3. 中央岗亭
- **路由**: `/central`
- **标题**: 中央岗亭
- **功能**:
  - 车辆查询界面
  - 6个监控摄像头网格显示
  - 搜索和筛选功能
  - 车辆基本信息显示
  - 清收费操作面板

## 🚀 测试步骤

### 1. 访问系统
1. 打开浏览器访问: http://localhost:5177/
2. 默认进入出口岗亭页面
3. 观察顶部左侧的岗亭切换按钮组

### 2. 测试出口岗亭
1. 确认当前在出口岗亭页面
2. 验证"出口岗亭"按钮为激活状态（蓝色）
3. 验证页面标题显示"出口岗亭"
4. 验证页面内容包含：
   - 入口监控和出口监控视频
   - 车牌输入区域
   - 右侧控制面板
   - 各种功能按钮

### 3. 切换到中央岗亭
1. 点击"中央岗亭"按钮
2. 验证页面跳转到 `/central` 路径
3. 验证"中央岗亭"按钮变为激活状态
4. 验证页面标题显示"中央岗亭"
5. 验证页面内容包含：
   - 车辆查询标题和搜索框
   - 6个监控摄像头网格布局
   - 右侧车辆基本信息面板
   - 清收费操作按钮

### 4. 切换回出口岗亭
1. 点击"出口岗亭"按钮
2. 验证页面跳转回 `/` 路径
3. 验证"出口岗亭"按钮重新激活
4. 验证页面内容恢复为出口岗亭界面

## 📋 详细验证清单

### 出口岗亭页面验证
- [ ] 页面标题显示"出口岗亭"
- [ ] "出口岗亭"按钮为激活状态
- [ ] 显示入口监控和出口监控
- [ ] 车牌输入功能正常
- [ ] 右侧控制面板显示正确
- [ ] 状态栏显示"剩余车位"
- [ ] 所有弹框功能正常

### 中央岗亭页面验证
- [ ] 页面标题显示"中央岗亭"
- [ ] "中央岗亭"按钮为激活状态
- [ ] 显示"车辆查询"标题
- [ ] 搜索框和筛选功能正常
- [ ] 6个监控摄像头网格显示
- [ ] 右侧车辆基本信息面板显示
- [ ] 清收费按钮组显示正确
- [ ] 状态栏显示"在场车辆"
- [ ] 监控列表功能正常

### 切换功能验证
- [ ] 按钮状态正确切换
- [ ] 路由跳转正常
- [ ] 页面内容完全切换
- [ ] 浏览器地址栏更新
- [ ] 页面标题更新
- [ ] 无JavaScript错误

## 🎨 界面设计

### 岗亭切换按钮
```
[出口岗亭] [中央岗亭]  [入口列表]  页面标题
```

- **按钮组**: 使用NButtonGroup组件
- **图标**: 出口岗亭🚪，中央岗亭🏢
- **激活状态**: 蓝色主题色
- **非激活状态**: 默认灰色

### 页面布局差异

#### 出口岗亭
- 双视频监控（入口+出口）
- 车牌输入和识别
- 收费和放行控制
- 业务操作弹框

#### 中央岗亭
- 车辆查询界面
- 6个监控摄像头网格
- 车辆基本信息面板
- 清收费操作按钮

## 🔧 技术实现

### 路由配置
```typescript
{
  path: '/',
  name: 'dashboard',
  component: DashboardView,
  meta: { title: '出口岗亭' }
},
{
  path: '/central',
  name: 'central', 
  component: CentralBoothView,
  meta: { title: '中央岗亭' }
}
```

### 组件通信
```typescript
// TopMenuBar组件
const emit = defineEmits<{
  switchBooth: [boothType: string]
}>()

// 页面组件
const handleBoothSwitch = (boothType: string) => {
  if (boothType === 'central') {
    router.push('/central')
  } else {
    router.push('/')
  }
}
```

### 状态管理
- 当前岗亭状态通过props传递
- 路由变化自动更新按钮状态
- 页面标题通过路由meta配置

## 📊 数据差异

### 出口岗亭数据
- 剩余车位数
- 入口/出口摄像头状态
- 当前车辆信息
- 收费相关数据

### 中央岗亭数据
- 在场车辆总数
- 车辆查询关键词
- 选中车辆信息
- 监控摄像头状态
- 分页控制状态

## ✅ 成功标准

1. **功能完整性**
   - 两个岗亭页面都能正常访问
   - 切换功能响应迅速
   - 所有页面功能正常工作

2. **界面一致性**
   - 按钮状态正确显示
   - 页面布局美观统一
   - 响应式设计适配

3. **用户体验**
   - 切换操作直观简单
   - 页面加载流畅
   - 无明显延迟或错误

4. **技术稳定性**
   - 路由跳转稳定
   - 组件状态管理正确
   - 控制台无错误信息

## 🐛 常见问题

### 问题1: 按钮状态不更新
**原因**: currentBooth prop未正确传递
**解决**: 检查TopMenuBar组件的props配置

### 问题2: 页面跳转失败
**原因**: 路由配置错误或组件导入问题
**解决**: 检查router/index.ts配置和组件导入

### 问题3: 页面内容不显示
**原因**: 组件依赖缺失或样式问题
**解决**: 检查组件导入和CSS样式文件

## 📝 更新日志

### v1.0.0 (2025-01-08)
- ✅ 实现基础岗亭切换功能
- ✅ 创建中央岗亭页面
- ✅ 添加岗亭切换按钮
- ✅ 配置路由和导航
- ✅ 完成界面设计和样式

### 后续计划
- [ ] 添加更多监控点位
- [ ] 实现数据实时同步
- [ ] 优化页面加载性能
- [ ] 添加权限控制
