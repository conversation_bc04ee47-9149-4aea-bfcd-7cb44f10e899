/* 首页布局样式 - 所有页面统一使用 */

/* 主容器 */
.parking-management {
  width: 100vw;
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: fixed;
  top: 0;
  left: 0;
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
}

/* 顶部状态栏 */
.top-status-bar {
  height: 70px;
  background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  color: #ffffff;
  flex-shrink: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.status-left {
  display: flex;
  align-items: center;
  gap: 20px;
}

/* 菜单按钮 */
.menu-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  padding: 8px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
}

.menu-button:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  margin-top: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.menu-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.menu-item {
  padding: 12px 16px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.menu-item:hover {
  background: #f8f9fa;
  color: #1e3c72;
}

.menu-item:first-child {
  border-radius: 8px 8px 0 0;
}

.menu-item:last-child {
  border-radius: 0 0 8px 8px;
  border-bottom: none;
}

.menu-item-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 20px;
}

.logo-text {
  font-size: 1.8em;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.15);
  padding: 8px 16px;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-icon {
  font-size: 1.3em;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.status-text {
  font-size: 0.95em;
  font-weight: 500;
}

.status-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.current-time {
  font-size: 1.3em;
  font-weight: 600;
  color: #ffffff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 20px;
  border-radius: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

.status-info {
  display: flex;
  align-items: center;
  gap: 20px;
}

.total-label {
  font-size: 0.95em;
  opacity: 0.9;
  font-weight: 500;
}

.total-amount {
  font-size: 1.2em;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.15);
  padding: 8px 15px;
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.user-icon {
  font-size: 1.2em;
}

.username {
  font-size: 0.95em;
  font-weight: 500;
}

.dropdown-arrow {
  font-size: 0.8em;
  opacity: 0.8;
  transition: transform 0.3s ease;
}

.user-info.active .dropdown-arrow {
  transform: rotate(180deg);
}

/* 用户下拉菜单 */
.user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 150px;
  z-index: 1000;
  margin-top: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.user-dropdown-item {
  padding: 12px 16px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.user-dropdown-item:hover {
  background: #f8f9fa;
  color: #1e3c72;
}

.user-dropdown-item:first-child {
  border-radius: 8px 8px 0 0;
}

.user-dropdown-item:last-child {
  border-radius: 0 0 8px 8px;
  border-bottom: none;
}

.user-dropdown-item.logout {
  color: #dc3545;
}

.user-dropdown-item.logout:hover {
  background: #f8d7da;
  color: #721c24;
}

/* 菜单按钮样式 */
.menu-button {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  padding: 8px 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
}

.menu-button:hover,
.menu-button.active {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  margin-top: 8px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.3s ease;
}

.menu-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.menu-item {
  padding: 12px 16px;
  color: #333333;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 500;
}

.menu-item:hover {
  background: #f8f9fa;
  color: #1e3c72;
}

.menu-item:first-child {
  border-radius: 8px 8px 0 0;
}

.menu-item:last-child {
  border-radius: 0 0 8px 8px;
  border-bottom: none;
}

.menu-item-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.refresh-btn, .exit-btn {
  background: rgba(255, 255, 255, 0.15);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #ffffff;
  padding: 8px 15px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9em;
  transition: all 0.3s ease;
  font-weight: 500;
}

.refresh-btn:hover, .exit-btn:hover {
  background: rgba(255, 255, 255, 0.25);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 主内容区域 - 移除backdrop-filter以提升浏览器性能 */
.main-content-area {
  flex: 1;
  display: flex;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  /* backdrop-filter: blur(10px); 移除以提升浏览器性能 */
}

/* 左侧导航面板 */
.left-nav-panel {
  width: 300px;
  background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  overflow: hidden;
}

.nav-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(90deg, #1e3c72 0%, #2a5298 100%);
  color: #ffffff;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-header h3 {
  margin: 0;
  font-size: 1.2em;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.nav-list {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-list::-webkit-scrollbar {
  width: 6px;
}

.nav-list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.nav-list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  margin: 3px 15px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #ecf0f1;
  position: relative;
  overflow: hidden;
}

.nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 4px;
  background: #3498db;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.nav-item:hover {
  background: rgba(52, 152, 219, 0.15);
  transform: translateX(5px);
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.2);
}

.nav-item:hover::before {
  transform: scaleY(1);
}

.nav-item.active {
  background: linear-gradient(90deg, rgba(52, 152, 219, 0.2) 0%, rgba(52, 152, 219, 0.1) 100%);
  box-shadow: 0 4px 20px rgba(52, 152, 219, 0.3);
  color: #ffffff;
  transform: translateX(5px);
}

.nav-item.active::before {
  transform: scaleY(1);
}

.nav-item.has-error {
  border-left: 3px solid #e74c3c;
  background: rgba(231, 76, 60, 0.1);
}

.nav-item.offline {
  opacity: 0.6;
}

.nav-icon {
  font-size: 1.3em;
  margin-right: 15px;
  min-width: 24px;
  text-align: center;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.3));
}

.nav-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 4px;
}

.nav-text {
  font-size: 1em;
  font-weight: 600;
  color: inherit;
}

.nav-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-text {
  font-size: 0.8em;
  opacity: 0.8;
  font-weight: 400;
}

.nav-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7em;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.nav-badge.normal {
  background: #27ae60;
  color: #ffffff;
}

.nav-badge.error {
  background: #e74c3c;
  color: #ffffff;
  /* animation: pulse 2s infinite; 移除动画以提升性能 */
}

.nav-badge.warning {
  background: #f39c12;
  color: #ffffff;
}

.nav-badge.offline {
  background: #95a5a6;
  color: #ffffff;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.7; }
  100% { opacity: 1; }
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #27ae60;
  box-shadow: 0 0 6px rgba(39, 174, 96, 0.6);
  /* animation: statusPulse 2s infinite; 移除动画以提升性能 */
}

.status-indicator.offline {
  background: #e74c3c;
  box-shadow: 0 0 6px rgba(231, 76, 60, 0.6);
}

.status-indicator.error {
  background: #f39c12;
  box-shadow: 0 0 6px rgba(243, 156, 18, 0.6);
}

@keyframes statusPulse {
  0% { box-shadow: 0 0 6px rgba(39, 174, 96, 0.6); }
  50% { box-shadow: 0 0 12px rgba(39, 174, 96, 0.8); }
  100% { box-shadow: 0 0 6px rgba(39, 174, 96, 0.6); }
}

.status-text {
  font-size: 0.8em;
  opacity: 0.8;
}

.nav-badge {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 0.75em;
  font-weight: 600;
  min-width: 16px;
  text-align: center;
}

.nav-badge.error {
  background: #e74c3c;
  color: white;
}

.nav-badge.offline {
  background: #95a5a6;
  color: white;
}

.nav-badge.count {
  background: #f39c12;
  color: white;
}

/* 中央内容区域 */
.center-content-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  overflow: hidden;
  /* backdrop-filter: blur(10px); 移除以提升浏览器性能 */
  position: relative;
}

/* 左侧浮动入口列表面板 */
.left-floating-channel-panel {
  position: fixed;
  top: 0;
  left: 0;
  width: 320px;
  height: 100vh;
  background: white;
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  overflow: hidden;
  animation: slideInFromLeft 0.3s ease-out;
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}

@keyframes slideInFromLeft {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

.left-floating-channel-panel .panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin-top: 70px;
}

.left-floating-channel-panel .panel-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: white;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  font-size: 20px;
  color: white;
  cursor: pointer;
  padding: 0;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.channel-list {
  height: calc(100vh - 210px);
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.channel-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin: 4px 0;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: #495057;
  position: relative;
  overflow: hidden;
}

.channel-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background: linear-gradient(180deg, #667eea, #764ba2);
  transform: scaleY(0);
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.channel-item:hover {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.08), rgba(118, 75, 162, 0.08));
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.1);
}

.channel-item:hover::before {
  transform: scaleY(1);
}

.channel-item.active {
  background: linear-gradient(90deg, rgba(102, 126, 234, 0.15), rgba(118, 75, 162, 0.15));
  color: #2c3e50;
  font-weight: 600;
  transform: translateX(4px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

.channel-item.active::before {
  transform: scaleY(1);
}

.channel-icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

.channel-text {
  font-size: 14px;
  font-weight: 500;
}

/* 右侧控制面板 */
.right-control-panel {
  width: 420px;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  border-left: 1px solid rgba(255, 255, 255, 0.2);
}

.panel-header {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
}

.panel-header h3 {
  margin: 0;
  color: #2c3e50;
  font-size: 1.1em;
  font-weight: 600;
}

/* 视频监控区域样式 */
.video-top-section {
  display: flex;
  gap: 16px;
  padding: 16px;
  height: 50%;
}

.video-bottom-section {
  display: flex;
  flex-direction: column;
  padding: 0 16px 16px;
  height: 50%;
}

.video-window {
  flex: 1;
  width: 50%; /* 强制等宽 */
  max-width: 50%; /* 防止超出 */
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: visible; /* 允许按钮显示在外面 */
  border: 2px solid #e0e0e0;
}

.video-window.small {
  flex: 1;
  min-height: 120px;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  background: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  font-size: 12px;
  font-weight: 600;
  color: #333;
}

.video-title {
  color: #2c3e50;
}

.video-time {
  color: #666;
  font-size: 11px;
}

.video-content {
  height: calc(100% - 60px);
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f0f0f0;
  position: relative;
  overflow: visible; /* 允许按钮显示 */
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #999;
  text-align: center;
}

.camera-icon {
  font-size: 48px;
  margin-bottom: 8px;
  opacity: 0.6;
}

.camera-text {
  font-size: 14px;
  font-weight: 500;
}

.license-plate {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
  font-weight: bold;
  font-size: 14px;
}

.video-footer {
  padding: 6px 12px;
  background: #2c3e50;
  color: white;
  font-size: 11px;
  text-align: center;
}

.video-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 0 4px;
}

.controls-title {
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}

.controls-buttons {
  display: flex;
  gap: 8px;
}

.control-btn {
  background: #3498db;
  color: white;
  border: none;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: background 0.3s ease;
}

.control-btn:hover {
  background: #2980b9;
}

.video-grid {
  display: flex;
  gap: 12px;
  height: calc(100% - 80px);
}

.bottom-status {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
  padding: 8px 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
}

.status-text {
  color: #856404;
  font-size: 13px;
  font-weight: 500;
}

.handle-btn {
  background: #007bff;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background 0.3s ease;
}

.handle-btn:hover {
  background: #0056b3;
}

/* 出场管理面板样式 */
.exit-label {
  background: #dc3545;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
}

.license-plate-section {
  margin-bottom: 20px;
}

.plate-display {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px solid #e0e0e0;
}

.plate-prefix {
  background: #6c757d;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: bold;
  font-size: 16px;
  min-width: 40px;
  text-align: center;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.plate-prefix:hover {
  background: #5a6268;
  transform: translateY(-1px);
}

.plate-number-input {
  background: #007bff;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-weight: bold;
  font-size: 18px;
  font-family: monospace;
  letter-spacing: 2px;
  border: 2px solid #007bff;
  outline: none;
  flex: 1;
  text-align: center;
}

.plate-number-input:focus {
  border-color: #0056b3;
  box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.25);
}

.plate-number-input::placeholder {
  color: rgba(255, 255, 255, 0.7);
}

.modify-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.3s ease;
}

.modify-btn:hover {
  background: #218838;
}

/* 车牌键盘样式 */
.plate-keyboard {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.keyboard-row {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.key-btn {
  background: #ffffff;
  border: 1px solid #dee2e6;
  color: #333;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  font-size: 14px;
  min-width: 36px;
  text-align: center;
  transition: all 0.2s ease;
}

.key-btn:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

.key-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

/* 操作按钮区域 */
.action-section {
  margin-top: 20px;
}

.action-buttons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 16px;
}

.action-btn {
  padding: 10px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 600;
  font-size: 13px;
  transition: all 0.3s ease;
}

.action-btn.success {
  background: #28a745;
  color: white;
}

.action-btn.success:hover {
  background: #218838;
}

.action-btn.info {
  background: #17a2b8;
  color: white;
}

.action-btn.info:hover {
  background: #138496;
}

.action-btn.primary {
  background: #007bff;
  color: white;
}

.action-btn.primary:hover {
  background: #0056b3;
}

.action-btn.danger {
  background: #dc3545;
  color: white;
}

.action-btn.danger:hover {
  background: #c82333;
}

/* 车辆类型选择 */
.vehicle-type-section {
  border-top: 1px solid #e0e0e0;
  padding-top: 16px;
}

.type-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.type-selector {
  margin-bottom: 12px;
}

.type-label {
  display: block;
  margin-bottom: 6px;
  font-weight: 600;
  color: #495057;
  font-size: 14px;
}

.type-value {
  font-weight: 500;
  color: #212529;
  font-size: 14px;
  background: #ffffff;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
}

.type-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  transition: all 0.3s ease;
  cursor: pointer;
}

.type-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  background: #f8f9ff;
}

.type-select:hover {
  border-color: #adb5bd;
}

/* 弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  /* backdrop-filter: blur(4px); 移除以提升浏览器性能 */
  will-change: opacity;
}

.modal-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  color: #6c757d;
  cursor: pointer;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.prefix-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 12px;
}

.prefix-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  will-change: transform, box-shadow;
}

.prefix-btn:hover {
  border-color: #007bff;
  background: #f8f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.prefix-btn.active {
  border-color: #007bff;
  background: #007bff;
  color: white;
}

.prefix-code {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.prefix-name {
  font-size: 12px;
  color: #6c757d;
}

.prefix-btn.active .prefix-name {
  color: rgba(255, 255, 255, 0.8);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
}

.modal-btn {
  padding: 8px 20px;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-btn.cancel {
  background: #6c757d;
  color: white;
}

.modal-btn.cancel:hover {
  background: #5a6268;
}

.modal-btn.confirm {
  background: #007bff;
  color: white;
}

.modal-btn.confirm:hover {
  background: #0056b3;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .right-control-panel {
    width: 380px;
  }

  .video-top-section {
    flex-direction: column;
    height: 60%;
  }

  .video-bottom-section {
    height: 40%;
  }
}

@media (max-width: 768px) {
  .main-content-area {
    flex-direction: column;
  }

  .left-nav-panel {
    width: 100%;
    height: auto;
    max-height: 150px;
  }

  .nav-list {
    display: flex;
    overflow-x: auto;
    padding: 10px;
  }

  .nav-item {
    white-space: nowrap;
    margin: 0 5px;
  }

  .right-control-panel {
    width: 100%;
    height: auto;
    border-left: none;
    border-top: 1px solid #e0e0e0;
  }

  .video-top-section {
    padding: 8px;
    gap: 8px;
  }

  .video-bottom-section {
    padding: 0 8px 8px;
  }

  .keyboard-row {
    gap: 2px;
  }

  .key-btn {
    padding: 6px 8px;
    font-size: 12px;
    min-width: 28px;
  }
}
