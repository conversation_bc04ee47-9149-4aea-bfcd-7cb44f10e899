import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import authApi from '@/services/authApi'
import httpClient from '@/services/httpClient'
import type { LoginRequest } from '@/services/authApi'

export interface User {
  id: string
  username: string
  displayName: string
  role: 'admin' | 'operator' | 'viewer'
  permissions: string[]
  boothId: string
}

export interface LoginCredentials {
  username: string
  password: string
}

export const useAuthStore = defineStore('auth', () => {
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const isLoading = ref(false)

  // Computed properties
  const isAuthenticated = computed(() => !!user.value && !!token.value)
  const userRole = computed(() => user.value?.role || null)
  const userPermissions = computed(() => user.value?.permissions || [])

  // Initialize from localStorage
  const initializeAuth = () => {
    const savedToken = localStorage.getItem('parking_booth_token')
    const savedUser = localStorage.getItem('parking_booth_user')

    if (savedToken && savedUser) {
      try {
        token.value = savedToken
        user.value = JSON.parse(savedUser)
        // 设置HTTP客户端的token
        httpClient.setAccessToken(savedToken)
      } catch (error) {
        console.error('Failed to parse saved user data:', error)
        clearAuth()
      }
    }
  }

  // Login function
  const login = async (username: string, password: string): Promise<boolean> => {
    isLoading.value = true

    try {
      // 尝试直接登录
      const loginResponse = await authApi.directLogin({ username, password })
      console.log('loginResponse',loginResponse);
      if (loginResponse.data.status === 1 && loginResponse.data.data) {
        // 登录成功，从响应中提取token
        const authToken = loginResponse.data

        // 设置token
        token.value = authToken

        // 获取用户信息
        try {
          const userResponse = await authApi.getUserInfo(username)

          if (userResponse.status === 1 && userResponse.data) {
            // 构造用户对象
            const userData: User = {
              id: userResponse.data.id || username,
              username: username,
              displayName: userResponse.data.displayName || username,
              role: (userResponse.data.role as 'admin' | 'operator' | 'viewer') || 'operator',
              permissions: userResponse.data.permissions || ['parking.entry', 'parking.exit'],
              boothId: userResponse.data.boothId || '001'
            }

            user.value = userData

            // Save to localStorage (token已经在authApi.directLogin中保存了)
            localStorage.setItem('parking_booth_user', JSON.stringify(userData))

            return true
          }
        } catch (userError) {
          console.warn('获取用户信息失败，使用默认信息:', userError)

          // 如果获取用户信息失败，使用默认用户信息
          const defaultUser: User = {
            id: username,
            username: username,
            displayName: username,
            role: 'operator',
            permissions: ['parking.entry', 'parking.exit', 'parking.payment'],
            boothId: '001'
          }

          const authToken = `token_${Date.now()}_${username}`

          token.value = authToken
          user.value = defaultUser

          localStorage.setItem('parking_booth_token', authToken)
          localStorage.setItem('parking_booth_user', JSON.stringify(defaultUser))

          return true
        }
      }

      return false
    } catch (error) {
      console.error('Login failed:', error)

      // 如果API登录失败，回退到模拟登录（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.warn('API登录失败，使用模拟登录')
        const mockResponse = await mockLogin(username, password)

        if (mockResponse.success && mockResponse.token && mockResponse.user) {
          token.value = mockResponse.token
          user.value = mockResponse.user

          localStorage.setItem('parking_booth_token', mockResponse.token)
          localStorage.setItem('parking_booth_user', JSON.stringify(mockResponse.user))

          return true
        }
      }

      return false
    } finally {
      isLoading.value = false
    }
  }

  // Logout function
  const logout = async () => {
    try {
      // 调用API退出登录
      await authApi.logout()
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      // 无论API调用是否成功，都清除本地认证信息
      clearAuth()
    }
  }

  // Clear authentication data
  const clearAuth = () => {
    user.value = null
    token.value = null
    // 清除HTTP客户端中的token
    httpClient.clearAccessToken()
    localStorage.removeItem('parking_booth_user')
  }

  // Check if user has specific permission
  const hasPermission = (permission: string): boolean => {
    return userPermissions.value.includes(permission) || userRole.value === 'admin'
  }

  // Check if user has any of the specified roles
  const hasRole = (roles: string[]): boolean => {
    return userRole.value ? roles.includes(userRole.value) : false
  }

  // Refresh user data
  const refreshUser = async (): Promise<boolean> => {
    if (!token.value || !user.value) return false

    try {
      const response = await authApi.getUserInfo(user.value.username)

      if (response.status === 1 && response.data) {
        const updatedUser: User = {
          ...user.value,
          displayName: response.data.displayName || user.value.displayName,
          role: (response.data.role as 'admin' | 'operator' | 'viewer') || user.value.role,
          permissions: response.data.permissions || user.value.permissions
        }

        user.value = updatedUser
        localStorage.setItem('parking_booth_user', JSON.stringify(updatedUser))

        return true
      }

      return false
    } catch (error) {
      console.error('Failed to refresh user data:', error)
      clearAuth()
      return false
    }
  }

  // Change password
  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      const response = await authApi.changePassword({
        oldPassword,
        newPassword
      })

      if (response.status === 1) {
        return true
      }

      return false
    } catch (error) {
      console.error('Failed to change password:', error)
      throw error
    }
  }

  // Mock login function for demo
  const mockLogin = async (username: string, password: string): Promise<{
    success: boolean
    token?: string
    user?: User
    message?: string
  }> => {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Demo credentials
    const validCredentials = [
      {
        username: 'admin',
        password: 'admin123',
        user: {
          id: '1',
          username: 'admin',
          displayName: '系統管理員',
          role: 'admin' as const,
          permissions: ['*'],
          boothId: '001'
        }
      },
      {
        username: 'operator',
        password: 'operator123',
        user: {
          id: '2',
          username: 'operator',
          displayName: '操作員',
          role: 'operator' as const,
          permissions: ['parking.entry', 'parking.exit', 'parking.payment', 'hardware.control'],
          boothId: '001'
        }
      },
      {
        username: 'viewer',
        password: 'viewer123',
        user: {
          id: '3',
          username: 'viewer',
          displayName: '查看員',
          role: 'viewer' as const,
          permissions: ['parking.view', 'reports.view'],
          boothId: '001'
        }
      }
    ]

    const credential = validCredentials.find(
      cred => cred.username === username && cred.password === password
    )

    if (credential) {
      return {
        success: true,
        token: `mock_token_${Date.now()}_${credential.user.id}`,
        user: credential.user
      }
    } else {
      return {
        success: false,
        message: '用戶名或密碼錯誤'
      }
    }
  }

  // Initialize auth on store creation
  initializeAuth()

  return {
    // State
    user,
    token,
    isLoading,
    
    // Computed
    isAuthenticated,
    userRole,
    userPermissions,
    
    // Actions
    login,
    logout,
    clearAuth,
    hasPermission,
    hasRole,
    refreshUser,
    changePassword,
    initializeAuth
  }
})
