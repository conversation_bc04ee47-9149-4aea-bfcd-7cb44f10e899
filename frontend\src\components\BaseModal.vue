<template>
  <Teleport to="body">
    <div v-if="visible" class="modal-overlay" @click="handleOverlayClick">
      <div 
        class="modal-container" 
        :class="[sizeClass, { 'modal-fullscreen': fullscreen }]"
        @click.stop
      >
        <!-- 头部 -->
        <div class="modal-header" :class="headerClass">
          <h3 class="modal-title">{{ title }}</h3>
          <button 
            v-if="showCloseButton" 
            class="modal-close-btn" 
            @click="handleClose"
            aria-label="关闭"
          >
            ×
          </button>
        </div>
        
        <!-- 内容区域 -->
        <div class="modal-body" :class="bodyClass">
          <slot />
        </div>
        
        <!-- 底部 -->
        <div v-if="$slots.footer" class="modal-footer" :class="footerClass">
          <slot name="footer" />
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  visible: boolean
  title?: string
  size?: 'small' | 'medium' | 'large' | 'extra-large'
  fullscreen?: boolean
  showCloseButton?: boolean
  closeOnOverlay?: boolean
  headerClass?: string
  bodyClass?: string
  footerClass?: string
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  size: 'medium',
  fullscreen: false,
  showCloseButton: true,
  closeOnOverlay: true,
  headerClass: '',
  bodyClass: '',
  footerClass: ''
})

const emit = defineEmits<Emits>()

// 计算尺寸类名
const sizeClass = computed(() => {
  const sizeMap = {
    small: 'modal-small',
    medium: 'modal-medium', 
    large: 'modal-large',
    'extra-large': 'modal-extra-large'
  }
  return sizeMap[props.size]
})

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
  emit('close')
}

// 处理遮罩层点击
const handleOverlayClick = () => {
  if (props.closeOnOverlay) {
    handleClose()
  }
}
</script>

<style scoped>
/* 基础弹窗样式 - 优化性能版本 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  /* 移除 backdrop-filter 以提升性能 */
}

.modal-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  max-height: 90vh;
  display: flex;
  flex-direction: column;
  /* 简化阴影效果 */
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  /* 移除复杂动画 */
}

/* 尺寸变体 */
.modal-small {
  width: 90%;
  max-width: 400px;
}

.modal-medium {
  width: 90%;
  max-width: 600px;
}

.modal-large {
  width: 90%;
  max-width: 900px;
}

.modal-extra-large {
  width: 95%;
  max-width: 1200px;
}

.modal-fullscreen {
  width: 100vw;
  height: 100vh;
  max-width: none;
  max-height: none;
  border-radius: 0;
}

/* 头部样式 */
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
  background: #f8f9fa;
  flex-shrink: 0;
}

.modal-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  line-height: 1;
}

.modal-close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

/* 内容区域 */
.modal-body {
  padding: 20px;
  overflow-y: auto;
  flex: 1;
}

/* 底部样式 */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding: 16px 20px;
  border-top: 1px solid #e9ecef;
  background: #f8f9fa;
  flex-shrink: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .modal-container {
    width: 95%;
    max-height: 95vh;
  }
  
  .modal-header {
    padding: 12px 16px;
  }
  
  .modal-body {
    padding: 16px;
  }
  
  .modal-footer {
    padding: 12px 16px;
    flex-direction: column;
  }
  
  .modal-title {
    font-size: 14px;
  }
}

/* 滚动条优化 */
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
