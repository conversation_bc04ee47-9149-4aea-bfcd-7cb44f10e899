using Microsoft.AspNetCore.SignalR;
using ParkingBoothApi.DTOs;

namespace ParkingBoothApi.Hubs
{
    public class ParkingHub : Hub
    {
        private readonly ILogger<ParkingHub> _logger;

        public ParkingHub(ILogger<ParkingHub> logger)
        {
            _logger = logger;
        }

        public override async Task OnConnectedAsync()
        {
            _logger.LogInformation("Client connected: {ConnectionId}", Context.ConnectionId);
            await base.OnConnectedAsync();
        }

        public override async Task OnDisconnectedAsync(Exception? exception)
        {
            _logger.LogInformation("Client disconnected: {ConnectionId}", Context.ConnectionId);
            await base.OnDisconnectedAsync(exception);
        }

        /// <summary>
        /// Join a specific booth group for targeted notifications
        /// </summary>
        public async Task JoinBoothGroup(string boothId)
        {
            await Groups.AddToGroupAsync(Context.ConnectionId, $"Booth_{boothId}");
            _logger.LogInformation("Client {ConnectionId} joined booth group: {BoothId}", Context.ConnectionId, boothId);
        }

        /// <summary>
        /// Leave a specific booth group
        /// </summary>
        public async Task LeaveBoothGroup(string boothId)
        {
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, $"Booth_{boothId}");
            _logger.LogInformation("Client {ConnectionId} left booth group: {BoothId}", Context.ConnectionId, boothId);
        }

        /// <summary>
        /// Send printer command from client
        /// </summary>
        public async Task SendPrinterCommand(string command, string? printerName = null)
        {
            _logger.LogInformation("Printer command received: {Command} for printer: {PrinterName}", command, printerName ?? "default");

            // Broadcast to all clients in the same booth group
            await Clients.Group($"Booth_001").SendAsync("PrinterCommandReceived", new
            {
                Command = command,
                PrinterName = printerName,
                Timestamp = DateTime.UtcNow,
                ConnectionId = Context.ConnectionId
            });
        }

        /// <summary>
        /// Ping method for connection testing
        /// </summary>
        public async Task Ping()
        {
            await Clients.Caller.SendAsync("Pong", DateTime.UtcNow);
        }

        /// <summary>
        /// Send system notification to specific booth
        /// </summary>
        public async Task SendSystemNotification(string boothId, SystemNotificationDto notification)
        {
            await Clients.Group($"Booth_{boothId}").SendAsync("SystemNotification", notification);
        }

        /// <summary>
        /// Broadcast hardware status update
        /// </summary>
        public async Task BroadcastHardwareStatus(string boothId, HardwareStatusUpdateDto update)
        {
            await Clients.Group($"Booth_{boothId}").SendAsync("HardwareStatusUpdate", update);
        }

        /// <summary>
        /// Broadcast vehicle detection event
        /// </summary>
        public async Task BroadcastVehicleDetection(string boothId, VehicleDetectionEventDto detection)
        {
            await Clients.Group($"Booth_{boothId}").SendAsync("VehicleDetection", detection);
        }

        /// <summary>
        /// Broadcast payment completed event
        /// </summary>
        public async Task BroadcastPaymentCompleted(string boothId, PaymentCompletedEventDto payment)
        {
            await Clients.Group($"Booth_{boothId}").SendAsync("PaymentCompleted", payment);
        }
    }

    // DTOs for SignalR events
    public class SystemNotificationDto
    {
        public string Type { get; set; } = string.Empty; // info, warning, error, success
        public string Title { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool AutoClose { get; set; } = true;
    }

    public class HardwareStatusUpdateDto
    {
        public string DeviceType { get; set; } = string.Empty;
        public string Status { get; set; } = string.Empty;
        public bool Connected { get; set; }
        public Dictionary<string, object>? Data { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    }

    public class VehicleDetectionEventDto
    {
        public bool VehicleDetected { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public int SensorReading { get; set; }
        public string? Message { get; set; }
    }

    public class PaymentCompletedEventDto
    {
        public string TransactionId { get; set; } = string.Empty;
        public string LicensePlate { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string PaymentMethod { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public bool Success { get; set; }
    }
}
