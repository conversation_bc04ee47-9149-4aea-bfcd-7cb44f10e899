using Microsoft.AspNetCore.Mvc;
using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;
using ParkingBoothApi.Helper;

namespace ParkingBoothApi.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class HardwareController : ControllerBase
    {
        private readonly IHardwareService _hardwareService;
        private readonly ILogger<HardwareController> _logger;

        public HardwareController(IHardwareService hardwareService, ILogger<HardwareController> logger)
        {
            _hardwareService = hardwareService;
            _logger = logger;
        }

        /// <summary>
        /// Get printer status
        /// </summary>
        [HttpGet("printer/status")]
        public async Task<ActionResult<PrinterStatusDto>> GetPrinterStatus()
        {
            try
            {
                var status = await _hardwareService.GetPrinterStatusAsync();
                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving printer status");
                return StatusCode(500, "An error occurred while retrieving printer status");
            }
        }

        /// <summary>
        /// Print a parking ticket using RawPrinterHelper
        /// </summary>
        [HttpPost("printer/print")]
        public async Task<ActionResult<PrinterResponseDto>> PrintTicket([FromBody] PrintTicketDto printDto)
        {
            try
            {
                _logger.LogInformation("Printing ticket of type: {Type}", printDto.Type);
                var result = await _hardwareService.PrintTicketAsync(printDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error printing ticket");
                return StatusCode(500, new PrinterResponseDto
                {
                    Success = false,
                    Message = "An error occurred while printing ticket",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Test printer functionality
        /// </summary>
        [HttpPost("printer/test")]
        public async Task<ActionResult<PrinterResponseDto>> TestPrinter()
        {
            try
            {
                var result = await _hardwareService.TestPrinterAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing printer");
                return StatusCode(500, new PrinterResponseDto
                {
                    Success = false,
                    Message = "An error occurred while testing printer",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Open cash drawer using RawPrinterHelper
        /// </summary>
        [HttpPost("cashdrawer/open")]
        public async Task<ActionResult<CashDrawerResponseDto>> OpenCashDrawer([FromBody] OpenCashDrawerDto? openDto = null)
        {
            try
            {
                openDto ??= new OpenCashDrawerDto();
                _logger.LogInformation("Opening cash drawer");
                var result = await _hardwareService.OpenCashDrawerAsync(openDto);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening cash drawer");
                return StatusCode(500, new CashDrawerResponseDto
                {
                    Success = false,
                    Message = "An error occurred while opening cash drawer",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Test cash drawer functionality
        /// </summary>
        [HttpPost("cashdrawer/test")]
        public async Task<ActionResult<CashDrawerResponseDto>> TestCashDrawer()
        {
            try
            {
                var result = await _hardwareService.TestCashDrawerAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing cash drawer");
                return StatusCode(500, new CashDrawerResponseDto
                {
                    Success = false,
                    Message = "An error occurred while testing cash drawer",
                    Error = ex.Message
                });
            }
        }

        /// <summary>
        /// Get available printers on the system
        /// </summary>
        [HttpGet("printer/list")]
        public async Task<ActionResult<PrinterListResponseDto>> GetAvailablePrinters()
        {
            try
            {
                var result = await _hardwareService.GetAvailablePrintersAsync();
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available printers");
                return StatusCode(500, new PrinterListResponseDto
                {
                    Success = false,
                    Message = "An error occurred while getting available printers",
                    Error = ex.Message
                });
            }
        }
    }
}
