using ParkingBoothApi.DTOs;

namespace ParkingBoothApi.Interfaces
{
    public interface IHardwareService
    {
        // 打印机操作
        Task<PrinterStatusDto> GetPrinterStatusAsync();
        Task<PrinterResponseDto> PrintTicketAsync(PrintTicketDto printDto);
        Task<PrinterResponseDto> TestPrinterAsync();
        Task<PrinterListResponseDto> GetAvailablePrintersAsync();

        // 钱箱操作
        Task<CashDrawerResponseDto> OpenCashDrawerAsync(OpenCashDrawerDto openDto);
        Task<CashDrawerResponseDto> TestCashDrawerAsync();
    }
}
