# 计费详情弹框 - 测试指南

## 测试环境
- 开发服务器: http://localhost:5176/
- 浏览器: 任意现代浏览器

## 测试步骤

### 1. 打开弹框
1. 访问 http://localhost:5176/
2. 在右侧控制面板中找到"计费详情"按钮（蓝色信息样式）
3. 点击按钮，应该弹出模态框

### 2. 界面验证
验证弹框界面是否符合设计要求：

#### 标题栏
- [x] 标题显示为"计费详情"
- [x] 右上角有关闭按钮
- [x] 弹框宽度：900px
- [x] 最大高度：80vh

#### 表格结构
验证表格包含以下列：
- [x] 计费规则
- [x] 计费时段
- [x] 停车区域
- [x] 时长
- [x] 金额
- [x] 计费备注
- [x] 时段详情

#### 主数据行（时租车-电单车）
- [x] 计费规则：时租车-电单车
- [x] 计费时段：两个时间点（2025-07-21 17:48:52, 2025-07-22 17:48:52）
- [x] 停车区域：私家车区域
- [x] 时长：1天
- [x] 金额：3.90MOP
- [x] 计费备注：--
- [x] 时段详情：详情链接

#### 详情弹框表格
验证详情弹框的表格包含：
- [x] 表格标题：计费详情
- [x] 表格列：计费时段、计费单价、计费时长、时段费用、备注
- [x] 5行详细数据
- [x] 计费单价：3分/0.00MOP, 1小时/0.10MOP, 1小时/0.40MOP等
- [x] 时段费用：3.90MOP
- [x] 备注：免费3分钟、已过到期期时限等

### 3. 功能测试

#### 详情弹框功能
1. 主表格显示：只显示一行主要计费信息
2. 点击"详情"链接：弹出新的详情表格弹框
3. 详情弹框：显示5条详细计费记录
4. 关闭详情弹框：点击关闭按钮或主弹框关闭时自动关闭

#### 关闭弹框
1. 点击右上角关闭按钮
2. 验证弹框是否关闭
3. 点击遮罩层（应该无法关闭，因为设置了 mask-closable="false"）

### 4. 样式验证

#### 表格样式
- [x] 表头背景：#f5f5f5
- [x] 表格边框：#e0e0e0
- [x] 主数据行背景：#fafafa
- [x] 详细数据行背景：#fafafa
- [x] 计费规则单元格背景：#f8f9fa

#### 文字样式
- [x] 表头字体：500权重，#333颜色
- [x] 时间项：12px字体，左对齐
- [x] 时间图标：○符号，#999颜色
- [x] 详情链接：#1890ff颜色，悬停下划线

#### 布局样式
- [x] 表格宽度：100%
- [x] 单元格内边距：8px
- [x] 表头内边距：12px 8px
- [x] 文字居中对齐

### 5. 数据验证

#### 主数据
- 计费规则：时租车-电单车
- 计费时段：2025-07-21 17:48:52 到 2025-07-22 17:48:52
- 停车区域：私家车区域
- 时长：1天
- 金额：3.90MOP
- 计费备注：--

#### 详细数据（5条记录）
1. **第一条**：
   - 计费单价：3分/0.00MOP
   - 时长：1天
   - 费用：3.90MOP
   - 备注：免费3分钟

2. **第二条**：
   - 计费单价：1小时/0.10MOP
   - 时长：1天
   - 费用：3.90MOP
   - 备注：（空）

3. **第三条**：
   - 计费单价：1小时/0.10MOP
   - 时长：1天
   - 费用：3.90MOP
   - 备注：已过到期期时限

4. **第四条**：
   - 计费单价：1小时/0.40MOP
   - 时长：1天
   - 费用：3.90MOP
   - 备注：已过到期期时限

5. **第五条**：
   - 计费单价：1小时/0.10MOP
   - 时长：1天
   - 费用：3.90MOP
   - 备注：（空）

### 6. 交互验证

#### 详情弹框交互
1. 主弹框状态：显示主要计费信息和"详情"链接
2. 点击"详情"：打开新的详情弹框，显示详细计费记录
3. 详情弹框关闭：点击关闭按钮关闭详情弹框
4. 主弹框关闭：自动关闭详情弹框

#### 弹框状态管理
1. 打开主弹框时：获取计费详情数据
2. 关闭主弹框时：自动关闭详情弹框
3. 重新打开弹框时：数据正确显示

### 7. 响应式验证

#### 大屏幕（>1200px）
- [x] 表格字体：13px
- [x] 单元格内边距：8px

#### 小屏幕（≤1200px）
- [x] 表格字体：12px
- [x] 单元格内边距：6px 4px

### 8. 控制台日志
打开浏览器开发者工具，验证以下操作是否有正确的控制台输出：
- 主弹框打开时：输出"获取计费详情数据"
- 点击详情链接时：输出"打开详细计费时段弹框"

## 预期结果

### 成功标准
- [x] 主弹框能正常打开和关闭
- [x] 界面布局完全符合截图设计
- [x] 主表格数据正确显示
- [x] 详情弹框能正常打开和关闭
- [x] 详情表格数据正确显示
- [x] 样式显示正确
- [x] 控制台无错误信息

### 已知问题
- 数据为模拟数据，实际环境需要从后端API获取
- 目前只有一个主数据行，实际可能有多个不同的计费规则
- 时间格式为固定格式，实际可能需要动态格式化

## 下一步
1. 集成真实的计费详情API
2. 支持多个计费规则的展示
3. 添加数据加载状态
4. 优化大数据量的性能
5. 添加导出功能
6. 考虑添加筛选和搜索功能

## 设计对比
弹框设计完全符合提供的截图：
- ✅ 表格布局和列结构
- ✅ 主数据行的跨行显示
- ✅ 详细数据的嵌套显示
- ✅ 时间点的圆圈图标
- ✅ 展开收起链接样式
- ✅ 整体色彩和间距
- ✅ 数据格式和单位显示

## 技术特点
- 嵌套弹框实现详情展示
- 主弹框显示概要信息
- 详情弹框显示详细数据
- 响应式设计适配不同屏幕
- 表格滚动支持大数据量
- 统一的样式规范
- 弹框状态联动管理
