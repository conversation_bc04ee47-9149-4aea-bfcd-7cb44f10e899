import type { GlobalTheme } from 'naive-ui'

// 蓝色主题配置 - 专为停车收费系统设计
export const blueTheme: GlobalTheme = {
  name: 'blue-parking-theme',
  common: {
    // 主要颜色 - 蓝色系
    primaryColor: '#1976d2',
    primaryColorHover: '#1565c0',
    primaryColorPressed: '#0d47a1',
    primaryColorSuppl: '#42a5f5',
    
    // 信息颜色
    infoColor: '#2196f3',
    infoColorHover: '#1976d2',
    infoColorPressed: '#0d47a1',
    infoColorSuppl: '#64b5f6',
    
    // 成功颜色
    successColor: '#4caf50',
    successColorHover: '#388e3c',
    successColorPressed: '#2e7d32',
    successColorSuppl: '#81c784',
    
    // 警告颜色
    warningColor: '#ff9800',
    warningColorHover: '#f57c00',
    warningColorPressed: '#ef6c00',
    warningColorSuppl: '#ffb74d',
    
    // 错误颜色
    errorColor: '#f44336',
    errorColorHover: '#d32f2f',
    errorColorPressed: '#c62828',
    errorColorSuppl: '#e57373',
    
    // 文本颜色
    textColorBase: '#000000',
    textColor1: 'rgba(0, 0, 0, 0.87)',
    textColor2: 'rgba(0, 0, 0, 0.6)',
    textColor3: 'rgba(0, 0, 0, 0.38)',
    textColorDisabled: 'rgba(0, 0, 0, 0.26)',
    
    // 背景颜色
    bodyColor: '#ffffff',
    cardColor: '#ffffff',
    modalColor: '#ffffff',
    popoverColor: '#ffffff',
    tableColor: '#ffffff',
    
    // 边框颜色
    borderColor: 'rgba(0, 0, 0, 0.12)',
    
    // 分割线颜色
    dividerColor: 'rgba(0, 0, 0, 0.12)',
    
    // 输入框颜色
    inputColor: '#ffffff',
    inputColorDisabled: '#f5f5f5',
    
    // 悬停颜色
    hoverColor: 'rgba(25, 118, 210, 0.04)',

    // 可能缺少的颜色属性
    closeIconColor: 'rgba(0, 0, 0, 0.6)',
    closeIconColorHover: 'rgba(0, 0, 0, 0.8)',
    closeIconColorPressed: 'rgba(0, 0, 0, 0.9)',

    // 清除颜色
    clearColor: 'rgba(0, 0, 0, 0.04)',
    clearColorHover: 'rgba(0, 0, 0, 0.06)',
    clearColorPressed: 'rgba(0, 0, 0, 0.08)',

    // 滚动条颜色
    scrollbarColor: 'rgba(0, 0, 0, 0.25)',
    scrollbarColorHover: 'rgba(0, 0, 0, 0.4)',
    
    // 字体
    fontFamily: '"Microsoft YaHei", "PingFang SC", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontFamilyMono: 'Consolas, "Courier New", monospace',
    
    // 字体大小
    fontSize: '14px',
    fontSizeMini: '12px',
    fontSizeTiny: '12px',
    fontSizeSmall: '13px',
    fontSizeMedium: '14px',
    fontSizeLarge: '16px',
    fontSizeHuge: '18px',
    
    // 字重
    fontWeight: '400',
    fontWeightStrong: '600',
    
    // 行高
    lineHeight: '1.6',
    
    // 圆角
    borderRadius: '6px',
    borderRadiusSmall: '4px',
    
    // 阴影
    boxShadow1: '0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)',
    boxShadow2: '0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)',
    boxShadow3: '0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)',
    
    // 透明度
    opacityDisabled: '0.38',
    
    // 高度
    heightTiny: '22px',
    heightSmall: '28px',
    heightMedium: '34px',
    heightLarge: '40px',
    heightHuge: '46px',
    
    // 内边距
    paddingTiny: '0 8px',
    paddingSmall: '0 12px',
    paddingMedium: '0 16px',
    paddingLarge: '0 20px',
    paddingHuge: '0 24px',
    
    // 外边距
    marginTiny: '4px',
    marginSmall: '8px',
    marginMedium: '12px',
    marginLarge: '16px',
    marginHuge: '20px',
    
    // 图标大小
    iconSize: '16px',
    iconSizeSmall: '14px',
    iconSizeMedium: '16px',
    iconSizeLarge: '18px',
    iconSizeHuge: '20px',
    
    // 立方贝塞尔曲线
    cubicBezierEaseInOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    cubicBezierEaseOut: 'cubic-bezier(0.0, 0, 0.2, 1)',
    cubicBezierEaseIn: 'cubic-bezier(0.4, 0, 1, 1)',
    
    // 动画持续时间
    durationSlow: '0.3s',
    durationMedium: '0.2s',
    durationFast: '0.1s'
  }
}

// 停车系统专用颜色变量
export const parkingSystemColors = {
  // 主要蓝色调色板
  primary: {
    50: '#e3f2fd',
    100: '#bbdefb',
    200: '#90caf9',
    300: '#64b5f6',
    400: '#42a5f5',
    500: '#2196f3',
    600: '#1976d2',
    700: '#1565c0',
    800: '#0d47a1',
    900: '#0277bd'
  },
  
  // 状态颜色
  status: {
    online: '#4caf50',
    offline: '#f44336',
    warning: '#ff9800',
    processing: '#2196f3',
    idle: '#9e9e9e'
  },
  
  // 车辆类型颜色
  vehicleType: {
    car: '#1976d2',
    truck: '#ff5722',
    motorcycle: '#9c27b0',
    vip: '#ffc107'
  },
  
  // 支付状态颜色
  payment: {
    paid: '#4caf50',
    pending: '#ff9800',
    failed: '#f44336',
    refunded: '#9e9e9e'
  },
  
  // 背景渐变
  gradients: {
    primary: 'linear-gradient(135deg, #1976d2 0%, #42a5f5 100%)',
    header: 'linear-gradient(135deg, #0d47a1 0%, #1976d2 100%)',
    card: 'linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%)'
  }
}

// 深色主题配置（可选）
export const blueDarkTheme: GlobalTheme = {
  ...blueTheme,
  common: {
    ...blueTheme.common,
    // 深色主题的颜色覆盖
    bodyColor: '#121212',
    cardColor: '#1e1e1e',
    modalColor: '#1e1e1e',
    popoverColor: '#1e1e1e',
    tableColor: '#1e1e1e',
    inputColor: '#2d2d2d',
    inputColorDisabled: '#3d3d3d',
    
    textColorBase: '#ffffff',
    textColor1: 'rgba(255, 255, 255, 0.87)',
    textColor2: 'rgba(255, 255, 255, 0.6)',
    textColor3: 'rgba(255, 255, 255, 0.38)',
    textColorDisabled: 'rgba(255, 255, 255, 0.26)',
    
    borderColor: 'rgba(255, 255, 255, 0.12)',
    dividerColor: 'rgba(255, 255, 255, 0.12)',
    
    hoverColor: 'rgba(25, 118, 210, 0.08)'
  }
}

// 主题工具函数
export const themeUtils = {
  // 获取主题颜色
  getThemeColor: (colorName: keyof typeof parkingSystemColors.primary) => {
    return parkingSystemColors.primary[colorName]
  },
  
  // 获取状态颜色
  getStatusColor: (status: keyof typeof parkingSystemColors.status) => {
    return parkingSystemColors.status[status]
  },
  
  // 创建带透明度的颜色
  withOpacity: (color: string, opacity: number) => {
    return `${color}${Math.round(opacity * 255).toString(16).padStart(2, '0')}`
  }
}
