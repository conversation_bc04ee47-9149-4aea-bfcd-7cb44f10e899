<template>
  <BaseModal
    :visible="visible"
    title="免费放行"
    size="small"
    @update:visible="handleUpdateVisible"
    @close="handleClose"
  >
    <!-- 弹框内容 -->
    <div class="free-pass-content">
      <!-- 按钮组 -->
      <div class="button-group">
        <button 
          class="action-btn primary-btn"
          :class="{ active: selectedReason === 'leadership' }"
          @click="selectReason('leadership')"
        >
          领导同意免费
        </button>
        <button 
          class="action-btn secondary-btn"
          :class="{ active: selectedReason === 'internal' }"
          @click="selectReason('internal')"
        >
          内部车
        </button>
      </div>

      <!-- 输入框 -->
      <div class="input-section">
        <label class="input-label">自定义输入免费放行原因</label>
        <textarea
          v-model="customReason"
          class="reason-input"
          placeholder="请输入免费放行原因..."
          rows="3"
        />
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button class="footer-btn cancel-btn" @click="handleCancel">
        取消
      </button>
      <button class="footer-btn confirm-btn" @click="handleConfirm">
        确定
      </button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import BaseModal from './BaseModal.vue'

defineOptions({
  name: 'FreePassModal'
})

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', reason: string): void
  (e: 'cancel'): void
}

const emit = defineEmits<Emits>()

// 响应式数据
const selectedReason = ref<'leadership' | 'internal' | ''>('')
const customReason = ref('')

// 选择原因
const selectReason = (reason: 'leadership' | 'internal') => {
  selectedReason.value = reason
  if (reason === 'leadership') {
    customReason.value = '领导同意免费'
  } else if (reason === 'internal') {
    customReason.value = '内部车'
  }
}

// 处理弹框显示状态变化
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

// 处理关闭
const handleClose = () => {
  emit('update:visible', false)
}

// 处理取消
const handleCancel = () => {
  emit('cancel')
  emit('update:visible', false)
}

// 处理确认
const handleConfirm = () => {
  const reason = customReason.value.trim()
  if (reason) {
    emit('confirm', reason)
    emit('update:visible', false)
  }
}

// 监听弹框显示状态，重置数据
watch(() => props.visible, (newVisible) => {
  if (newVisible) {
    // 弹框打开时重置数据
    selectedReason.value = ''
    customReason.value = ''
  }
})
</script>

<style scoped>
.free-pass-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 按钮组样式 */
.button-group {
  display: flex;
  gap: 12px;
}

.action-btn {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  background: #fff;
  color: #666;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.primary-btn {
  background: #f0f8ff;
  border-color: #1890ff;
  color: #1890ff;
}

.primary-btn:hover {
  background: #e6f7ff;
  border-color: #40a9ff;
}

.primary-btn.active {
  background: #1890ff;
  color: white;
}

.secondary-btn {
  background: #f5f5f5;
  border-color: #d9d9d9;
  color: #666;
}

.secondary-btn:hover {
  background: #e6e6e6;
  border-color: #bfbfbf;
}

.secondary-btn.active {
  background: #595959;
  color: white;
  border-color: #595959;
}

/* 输入区域样式 */
.input-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.input-label {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.reason-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.reason-input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.reason-input::placeholder {
  color: #bfbfbf;
}

/* 底部按钮样式 */
.footer-btn {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
  min-width: 80px;
}

.cancel-btn {
  background: #fff;
  color: #666;
}

.cancel-btn:hover {
  background: #f5f5f5;
  border-color: #bfbfbf;
}

.confirm-btn {
  background: #1890ff;
  color: white;
  border-color: #1890ff;
}

.confirm-btn:hover {
  background: #40a9ff;
  border-color: #40a9ff;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-group {
    flex-direction: column;
  }
  
  .action-btn {
    padding: 10px 14px;
  }
}
</style>
