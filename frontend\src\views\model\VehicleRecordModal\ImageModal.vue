<template>
  <!-- 图片查看弹框 -->
  <NModal
    :show="visible"
    preset="card"
    title="车辆图片"
    size="huge"
    :mask-closable="true"
    :closable="true"
    style="width: 90vw; height: 85vh;"
    @update:show="handleUpdateVisible"
  >
    <div v-if="record" class="image-modal-content">
      <!-- 车辆基本信息 -->
      <div class="image-info">
        <div class="info-item">
          <span class="info-label">车牌号码:</span>
          <span class="info-value">{{ record.plateNumber }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">进入时间:</span>
          <span class="info-value">{{ record.entryTime }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">离开时间:</span>
          <span class="info-value">{{ record.exitTime || '未离开' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">通道名称:</span>
          <span class="info-value">{{ record.channelName }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">车辆类型:</span>
          <span class="info-value">{{ record.vehicleType }}</span>
        </div>
      </div>

      <!-- 图片展示区域 -->
      <div class="image-gallery">
        <div class="image-section">
          <h3>进入时图片</h3>
          <div class="image-grid">
            <NImage
              v-for="(image, index) in entryImages"
              :key="`entry-${index}`"
              :src="image.url"
              :alt="image.alt"
              width="300"
              height="200"
              object-fit="cover"
              :preview-disabled="false"
              show-toolbar-tooltip
              class="vehicle-image"
            />
          </div>
        </div>

        <div class="image-section">
          <h3>离开时图片</h3>
          <div class="image-grid">
            <NImage
              v-for="(image, index) in exitImages"
              :key="`exit-${index}`"
              :src="image.url"
              :alt="image.alt"
              width="300"
              height="200"
              object-fit="cover"
              :preview-disabled="false"
              show-toolbar-tooltip
              class="vehicle-image"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作区域 -->
    <template #footer>
      <div class="modal-footer">
        <NSpace>
          <NButton @click="handleClose">关闭</NButton>
        </NSpace>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { NModal, NButton, NSpace, NImage } from 'naive-ui'

defineOptions({
  name: 'ImageModal'
})

// ==================== 接口定义 ====================
interface VehicleRecord {
  id: number
  plateNumber: string
  entryTime: string
  exitTime?: string
  vehicleType: string
  channelName: string
  vehicleStatus: string
  entryType: string
  entryStatus: string
  remarks: string
  operator: string
  isAbnormal: boolean
  isPaid: boolean
}

interface ImageInfo {
  url: string
  alt: string
}

// ==================== Props 和 Emits ====================
interface Props {
  visible: boolean
  record: VehicleRecord | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 计算属性 ====================
/**
 * 进入时图片
 */
const entryImages = computed<ImageInfo[]>(() => {
  if (!props.record) return []
  
  return [
    {
      url: `https://picsum.photos/600/400?random=${props.record.id}`,
      alt: '进入时车辆全景'
    },
    {
      url: `https://picsum.photos/600/400?random=${props.record.id + 10}`,
      alt: '进入时车牌特写'
    }
  ]
})

/**
 * 离开时图片
 */
const exitImages = computed<ImageInfo[]>(() => {
  if (!props.record) return []
  
  return [
    {
      url: `https://picsum.photos/600/400?random=${props.record.id + 20}`,
      alt: '离开时车辆全景'
    },
    {
      url: `https://picsum.photos/600/400?random=${props.record.id + 30}`,
      alt: '离开时车牌特写'
    }
  ]
})

// ==================== 方法定义 ====================
/**
 * 处理弹框显示状态更新
 */
const handleUpdateVisible = (visible: boolean) => {
  emit('update:visible', visible)
}

/**
 * 关闭弹框
 */
const handleClose = () => {
  emit('update:visible', false)
}
</script>

<style scoped>
/* 图片弹框样式 */
.image-modal-content {
  padding: 20px;
}

.image-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  align-items: center;
  min-width: 200px;
}

.info-label {
  font-weight: 600;
  color: #333;
  margin-right: 8px;
}

.info-value {
  color: #666;
}

.image-gallery {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.image-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}

.image-grid {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.vehicle-image {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.vehicle-image:hover {
  transform: scale(1.02);
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
}
</style>
