import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { getApiConfig, RESPONSE_STATUS } from '@/config/api'

// API响应接口
export interface ApiResponse<T = any> {
  status: number
  msg?: string
  data?: T
  result?: number
  error?: string
  error_description?: string
}

// 登录响应接口
export interface LoginResponse {
  access_token: string
  token_type: string
  expires_in: number
  scope: string
  refresh_token?: string
}

// 用户信息接口
export interface UserInfo {
  id: string
  username: string
  displayName: string
  role: string
  permissions: string[]
  boothId: string
}

class HttpClient {
  private instance: AxiosInstance
  private baseURL: string

  constructor() {
    const config = getApiConfig()
    this.baseURL = config.BASE_URL

    this.instance = axios.create({
      baseURL: this.baseURL,
      timeout: config.TIMEOUT,
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'lang': 'zh_CN'
      }
    })

    this.setupInterceptors()
  }

  private setupInterceptors() {
    // 请求拦截器
    this.instance.interceptors.request.use(
      (config) => {
        // 添加token
        const token = localStorage.getItem('parking_booth_token')
        if (token) {
          config.headers['accesstoken'] = token
        }

        // 开发环境日志
        if (process.env.NODE_ENV === 'development') {
          console.log('🚀 API Request:', config.url, config.data)
        }

        return config
      },
      (error) => {
        console.error('❌ Request Error:', error)
        return Promise.reject(error)
      }
    )

    // 响应拦截器
    this.instance.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        // 开发环境日志
        if (process.env.NODE_ENV === 'development') {
          console.log('✅ API Response:', response.config.url, response.data)
        }

        const { data } = response

        // 处理成功响应
        if (data.status === RESPONSE_STATUS.SUCCESS || data.result === 0) {
          return response
        }

        // 处理登录过期
        if (data.status === RESPONSE_STATUS.UNAUTHORIZED ||
            data.status === RESPONSE_STATUS.TOKEN_EXPIRED ||
            data.status === RESPONSE_STATUS.PERMISSION_DENIED ||
            data.error === 'invalid_token') {
          this.handleAuthError(data.msg || '登录已过期')
          return Promise.reject(new Error(data.msg || '登录已过期'))
        }

        // 处理OAuth token响应
        if (response.config.url?.includes('/oauth/token') && (data as any).access_token) {
          return response
        }

        // 处理登录错误
        if (data.error === 'invalid_grant') {
          return Promise.reject(new Error('用户名或密码错误'))
        }

        // 处理其他错误
        const errorMsg = data.msg || data.error_description || '未知错误'
        return Promise.reject(new Error(errorMsg))
      },
      (error) => {
        console.error('❌ Response Error:', error)
        
        if (error.response?.status === 401) {
          this.handleAuthError('登录已过期')
        } else if (error.response?.status >= 500) {
          console.error('服务器错误:', error.response.status)
        } else if (error.code === 'NETWORK_ERROR' || error.code === 'ECONNABORTED') {
          console.error('网络错误或请求超时')
        }

        return Promise.reject(error)
      }
    )
  }

  private handleAuthError(message: string) {
    // 清除HTTP客户端中的token
    this.clearAccessToken()

    // 清除本地存储的认证信息
    localStorage.removeItem('parking_booth_user')

    // 显示错误消息
    console.error('认证错误:', message)

    // 跳转到登录页面
    if (window.location.pathname !== '/login') {
      window.location.href = '/login'
    }
  }

  // GET请求
  async get<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.get(url, config)
  }

  // POST请求
  async post<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, config)
  }

  // PUT请求
  async put<T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.put(url, data, config)
  }

  // DELETE请求
  async delete<T = any>(url: string, config?: AxiosRequestConfig): Promise<ApiResponse<T>> {
    return this.instance.delete(url, config)
  }

  // 表单提交
  async postForm<T = any>(url: string, data?: any): Promise<ApiResponse<T>> {
    return this.instance.post(url, data, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'
      },
      transformRequest: [function (data) {
        const params = new URLSearchParams()
        for (const key in data) {
          if (data[key] !== null && data[key] !== undefined) {
            params.append(key, data[key])
          }
        }
        return params
      }]
    })
  }

  // 查询参数请求
  async getWithQuery<T = any>(url: string, params?: any): Promise<ApiResponse<T>> {
    return this.instance.get(url, { params })
  }

  // 文件上传
  async upload<T = any>(url: string, file: File, onProgress?: (progress: number) => void): Promise<ApiResponse<T>> {
    const formData = new FormData()
    formData.append('file', file)

    return this.instance.post(url, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
          onProgress(progress)
        }
      }
    })
  }

  // 文件下载
  async download(url: string, params?: any): Promise<ArrayBuffer> {
    const response = await this.instance.get(url, {
      params,
      responseType: 'arraybuffer'
    })
    return response.data
  }

  // 获取基础URL
  getBaseURL(): string {
    return this.baseURL
  }

  // 设置基础URL
  setBaseURL(url: string): void {
    this.baseURL = url
    this.instance.defaults.baseURL = url
  }

  // 设置访问令牌
  setAccessToken(token: string): void {
    // 设置到默认请求头中
    this.instance.defaults.headers.common['accesstoken'] = token
    // 也保存到localStorage以便请求拦截器使用
    localStorage.setItem('parking_booth_token', token)
  }

  // 清除访问令牌
  clearAccessToken(): void {
    delete this.instance.defaults.headers.common['accesstoken']
    localStorage.removeItem('parking_booth_token')
  }
}

// 创建单例实例
export const httpClient = new HttpClient()
export default httpClient
