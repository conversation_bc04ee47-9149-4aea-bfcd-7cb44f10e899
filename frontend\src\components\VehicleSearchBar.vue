<template>
  <div class="vehicle-search-bar">
    <!-- 标题 -->
    <div class="search-title">
      <h2>车辆查询</h2>
    </div>

    <!-- 搜索区域 -->
    <div class="search-content">
      <!-- 左侧：时间范围选择 -->
      <div class="time-range-section">
        <NDatePicker
          v-model:value="timeRange"
          type="datetimerange"
          clearable
          placeholder="开始时间 - 结束时间"
          format="yyyy-MM-dd HH:mm:ss"
          value-format="yyyy-MM-dd HH:mm:ss"
          class="time-picker"
          size="large"
        />
      </div>

      <!-- 中间：搜索输入框 -->
      <div class="search-input-section">
        <NInput
          v-model:value="searchKeyword"
          placeholder="支持车牌、入场小票、刷卡查询"
          size="large"
          class="search-input"
          clearable
          @keyup.enter="handleSearch"
        >
          <template #prefix>
            <NIcon :component="SearchOutline" />
          </template>
        </NInput>
      </div>

      <!-- 右侧：操作按钮 -->
      <div class="action-buttons">
        <!-- 空车牌复选框 -->
        <div class="checkbox-section">
          <NCheckbox v-model:checked="isEmptyPlate" size="large">
            空车牌
          </NCheckbox>
        </div>

        <!-- 查询按钮 -->
        <NButton
          type="primary"
          size="large"
          class="search-btn"
          @click="handleSearch"
        >
          查询
        </NButton>

        <!-- 入场小票按钮 -->
        <NButton
          type="info"
          size="large"
          class="ticket-btn"
          @click="handleTicketEntry"
        >
          入场小票
        </NButton>

        <!-- 无法识别按钮 -->
        <NButton
          type="success"
          size="large"
          class="unrecognized-btn"
          @click="handleUnrecognized"
        >
          无法识别
        </NButton>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { NDatePicker, NInput, NButton, NCheckbox, NIcon } from 'naive-ui'
import { SearchOutline } from '@vicons/ionicons5'

defineOptions({
  name: 'VehicleSearchBar'
})

// ==================== Props & Emits ====================
interface Emits {
  (e: 'search', data: { timeRange: [string, string] | null; keyword: string; isEmptyPlate: boolean }): void
  (e: 'ticket-entry'): void
  (e: 'unrecognized'): void
}

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const timeRange = ref<[string, string] | null>(null)
const searchKeyword = ref('')
const isEmptyPlate = ref(false)

// ==================== 方法 ====================
const handleSearch = () => {
  emit('search', {
    timeRange: timeRange.value,
    keyword: searchKeyword.value.trim(),
    isEmptyPlate: isEmptyPlate.value
  })
}

const handleTicketEntry = () => {
  emit('ticket-entry')
}

const handleUnrecognized = () => {
  emit('unrecognized')
}

// 清空搜索条件
const clearSearch = () => {
  timeRange.value = null
  searchKeyword.value = ''
  isEmptyPlate.value = false
}

// 暴露方法给父组件
defineExpose({
  clearSearch
})
</script>

<style scoped>
.vehicle-search-bar {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.search-title {
  margin-bottom: 20px;
}

.search-title h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.search-content {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.time-range-section {
  flex-shrink: 0;
}

.time-picker {
  width: 320px;
}

.search-input-section {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.checkbox-section {
  display: flex;
  align-items: center;
}

.search-btn {
  background: #4285f4;
  border-color: #4285f4;
  min-width: 80px;
}

.search-btn:hover {
  background: #3367d6;
  border-color: #3367d6;
}

.ticket-btn {
  background: #17a2b8;
  border-color: #17a2b8;
  min-width: 90px;
}

.ticket-btn:hover {
  background: #138496;
  border-color: #138496;
}

.unrecognized-btn {
  background: #28a745;
  border-color: #28a745;
  min-width: 90px;
}

.unrecognized-btn:hover {
  background: #218838;
  border-color: #218838;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .search-content {
    flex-direction: column;
    align-items: stretch;
  }

  .time-picker {
    width: 100%;
  }

  .action-buttons {
    justify-content: flex-end;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  .action-buttons {
    justify-content: center;
  }

  .search-btn,
  .ticket-btn,
  .unrecognized-btn {
    min-width: 70px;
    font-size: 14px;
  }
}
</style>
