import { createRouter, createWebHistory } from 'vue-router'
import LoginView from '../views/LoginView.vue'
import DashboardView from '../views/DashboardView.vue'
import CentralBoothView from '../views/CentralBoothView.vue'
import { useAuthStore } from '../stores/auth'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: LoginView,
      meta: {
        title: '用户登录',
        requiresAuth: false
      }
    },
    {
      path: '/',
      name: 'dashboard',
      component: DashboardView,
      meta: {
        title: '出口岗亭',
        requiresAuth: true
      }
    },
    {
      path: '/central',
      name: 'central',
      component: CentralBoothView,
      meta: {
        title: '中央岗亭',
        requiresAuth: true
      }
    },

    {
      path: '/:pathMatch(.*)*',
      redirect: '/'
    }
  ]
})

// Navigation guard for authentication and page titles
router.beforeEach((to, _from, next) => {
  const authStore = useAuthStore()

  // Set page title
  if (to.meta?.title) {
    document.title = `${to.meta.title} - HONGRUI停车收费系统`
  } else {
    document.title = 'HONGRUI停车收费系统'
  }

  // Check authentication
  if (to.meta?.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.name === 'login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
