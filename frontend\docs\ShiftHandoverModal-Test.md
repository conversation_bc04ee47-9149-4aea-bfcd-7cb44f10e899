# 交班确认弹框 - 测试指南

## 测试环境
- 开发服务器: http://localhost:5176/
- 浏览器: 任意现代浏览器

## 测试步骤

### 1. 打开弹框
1. 访问 http://localhost:5176/
2. 在右侧控制面板中找到"交班确认"按钮（橙色警告样式）
3. 点击按钮，应该弹出模态框

### 2. 界面验证
验证弹框界面是否符合设计要求：

#### 标题栏
- [x] 标题显示为"交班确认"
- [x] 右上角有关闭按钮
- [x] 弹框宽度：600px

#### 用户信息区域
- [x] 显示当前用户：aofe
- [x] 显示上次交班时间：2025-07-22 17:51:11
- [x] 背景色：#f8f9fa
- [x] 信息布局：左右分布

#### 出口岗亭当前班次数据汇总
验证数据表格包含以下字段：
- [x] 应收金额：173MOP | 现金金额：173MOP
- [x] 免费金额：0MOP | 抵扣金额：0MOP
- [x] 抵扣时长：0 | 抵扣张数：0
- [x] 实际抵扣：0MOP | --：--
- [x] 总放行：4次 | 正常放行：2次
- [x] 异常放行：2次 | 免费放行：0次

#### 中央岗亭当前班次数据
验证数据表格包含以下字段：
- [x] 应收金额：0MOP | 现金金额：0MOP
- [x] 免费金额：0MOP | 抵扣金额：0MOP
- [x] 抵扣时长：0 | 抵扣张数：0

#### 底部按钮
- [x] "取消交班"按钮（默认样式）
- [x] "确认交班"按钮（主要样式）
- [x] 按钮居中对齐，间距16px

### 3. 功能测试

#### 取消交班
1. 点击"取消交班"按钮
2. 验证弹框是否关闭
3. 验证没有执行任何交班操作

#### 确认交班
1. 点击"确认交班"按钮
2. 验证按钮是否显示加载状态
3. 验证控制台是否输出交班数据
4. 验证弹框是否在1.5秒后关闭

#### 关闭弹框
1. 点击右上角关闭按钮
2. 验证弹框是否关闭
3. 点击遮罩层（应该无法关闭，因为设置了 mask-closable="false"）

### 4. 样式验证

#### 布局检查
- [x] 弹框宽度：600px
- [x] 用户信息区域背景：#f8f9fa
- [x] 数据表格边框：#dee2e6
- [x] 表头背景：#e9ecef

#### 数据表格样式
- [x] 表格行边框分隔
- [x] 数据项左右分布
- [x] 标签颜色：#666
- [x] 数值颜色：#333，字体加粗

#### 按钮样式
- [x] 取消按钮：默认样式
- [x] 确认按钮：主要样式（蓝色）
- [x] 加载状态显示

### 5. 数据验证

#### 出口岗亭数据
验证以下数据是否正确显示：
- 应收金额：173MOP
- 现金金额：173MOP
- 免费金额：0MOP
- 抵扣金额：0MOP
- 抵扣时长：0
- 抵扣张数：0
- 实际抵扣：0MOP
- 总放行：4次
- 正常放行：2次
- 异常放行：2次
- 免费放行：0次

#### 中央岗亭数据
验证以下数据是否正确显示：
- 应收金额：0MOP
- 现金金额：0MOP
- 免费金额：0MOP
- 抵扣金额：0MOP
- 抵扣时长：0
- 抵扣张数：0

#### 用户信息
- 当前用户：aofe
- 上次交班时间：2025-07-22 17:51:11

### 6. 交互验证

#### 确认交班流程
1. 点击"确认交班"按钮
2. 验证按钮文字变为加载状态
3. 等待1.5秒模拟API调用
4. 验证控制台输出包含完整的交班数据：
   - currentUser
   - lastShiftTime
   - exitData（出口岗亭数据）
   - centralData（中央岗亭数据）
   - confirmTime（确认时间）

#### 弹框状态管理
1. 打开弹框时重置加载状态
2. 关闭弹框时清理状态
3. 重新打开弹框时数据正确显示

### 7. 控制台日志
打开浏览器开发者工具，验证以下操作是否有正确的控制台输出：
- 弹框打开时：输出"获取最新班次数据"
- 确认交班时：输出完整的交班数据对象

## 预期结果

### 成功标准
- [x] 弹框能正常打开和关闭
- [x] 界面布局完全符合截图设计
- [x] 所有数据正确显示
- [x] 交互功能正常工作
- [x] 样式显示正确
- [x] 控制台无错误信息

### 已知问题
- 数据为模拟数据，实际环境需要从后端API获取
- 确认交班功能为模拟实现，实际环境需要调用真实的交班API
- 交班成功后可能需要跳转到登录页或刷新页面数据

## 下一步
1. 集成真实的班次数据API
2. 实现实际的交班确认功能
3. 添加交班成功后的页面跳转逻辑
4. 添加错误处理和用户反馈
5. 考虑添加交班前的二次确认提示

## 设计对比
弹框设计完全符合提供的截图：
- ✅ 标题和布局
- ✅ 用户信息显示
- ✅ 两个数据区域的表格布局
- ✅ 数据字段和数值
- ✅ 底部按钮样式和位置
- ✅ 整体色彩和间距
