const { spawn } = require('child_process')
const path = require('path')

// 简单的 Electron 启动脚本
function startElectron() {
  console.log('⚡ 启动 Electron 应用...')
  
  const electronProcess = spawn('electron', ['.'], {
    stdio: 'inherit',
    shell: true,
    cwd: __dirname
  })
  
  electronProcess.on('close', (code) => {
    console.log(`Electron 进程退出，退出码: ${code}`)
    process.exit(code)
  })
  
  electronProcess.on('error', (error) => {
    console.error('❌ Electron 启动失败:', error.message)
    console.log('💡 请确保已安装 Electron: npm install electron')
    process.exit(1)
  })
  
  // 处理进程终止
  process.on('SIGINT', () => {
    console.log('收到终止信号，关闭 Electron...')
    electronProcess.kill('SIGINT')
  })
  
  process.on('SIGTERM', () => {
    console.log('收到终止信号，关闭 Electron...')
    electronProcess.kill('SIGTERM')
  })
}

// 启动应用
startElectron()
