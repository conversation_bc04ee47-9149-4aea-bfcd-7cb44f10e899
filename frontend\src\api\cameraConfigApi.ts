import httpClient from '../services/httpClient'

// 摄像头配置接口
export interface CameraConfig {
  id: string
  name: string
  ip: string
  port: number
  type: 'zhenshi' | 'huaxia' | 'qianyi' | 'shijie' | 'xinlutong'
  username?: string
  password?: string
  channel?: string
}

// 臻识相机token响应
export interface ZhenshiTokenResponse {
  body: {
    port: number
    pubkey: string
  }
}

// 摄像头配置API
export const cameraConfigApi = {
  // 获取摄像头配置列表
  async getCameraConfigs(): Promise<CameraConfig[]> {
    try {
      const response = await httpClient.get('/api/camera/configs')
      return response.data || []
    } catch (error) {
      console.warn('获取摄像头配置失败，使用默认配置:', error)
      // 返回默认配置
      return [
        {
          id: 'entrance',
          name: '入口摄像头',
          ip: '*********',
          port: 9080,
          type: 'zhenshi',
          username: 'admin',
          password: 'admin123'
        },
        {
          id: 'exit',
          name: '出口摄像头', 
          ip: '**********',
          port: 9999,
          type: 'huaxia'
        }
      ]
    }
  },

  // 获取臻识相机token (需要先调用API获取公钥)
  async getZhenshiToken(ip: string, username: string, password: string): Promise<string> {
    try {
      // 第一步：获取公钥
      const response = await httpClient.post(`http://${ip}/api/login`, {
        username,
        password
      })
      
      const data: ZhenshiTokenResponse = response.data
      
      // 这里需要RSA加密，但为了简化，我们先返回一个模拟token
      // 在实际项目中，需要引入JSEncrypt库进行RSA加密
      const mockToken = btoa(`${username}:${password}:${Date.now()}`)
      
      return mockToken
    } catch (error) {
      console.error('获取臻识相机token失败:', error)
      throw new Error('无法获取摄像头认证token')
    }
  },

  // 测试摄像头连接
  async testCameraConnection(config: CameraConfig): Promise<boolean> {
    try {
      // 根据摄像头类型进行不同的测试
      if (config.type === 'zhenshi') {
        // 臻识相机：测试HTTP API
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), 5000)

        const response = await fetch(`http://${config.ip}/api/status`, {
          method: 'GET',
          signal: controller.signal
        })

        clearTimeout(timeoutId)
        return response.ok
      } else {
        // 其他摄像头：测试WebSocket连接
        return new Promise((resolve) => {
          const ws = new WebSocket(`ws://${config.ip}:${config.port}/`)
          const timeout = setTimeout(() => {
            ws.close()
            resolve(false)
          }, 5000)
          
          ws.onopen = () => {
            clearTimeout(timeout)
            ws.close()
            resolve(true)
          }
          
          ws.onerror = () => {
            clearTimeout(timeout)
            resolve(false)
          }
        })
      }
    } catch (error) {
      console.error('测试摄像头连接失败:', error)
      return false
    }
  }
}

export default cameraConfigApi
