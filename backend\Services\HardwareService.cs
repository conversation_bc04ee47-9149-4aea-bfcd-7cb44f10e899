using ParkingBoothApi.DTOs;
using ParkingBoothApi.Interfaces;
using ParkingBoothApi.Helper;
using System.Drawing.Printing;
using System.Text;

namespace ParkingBoothApi.Services
{
    public class HardwareService : IHardwareService
    {
        private readonly ILogger<HardwareService> _logger;
        private readonly IConfiguration _configuration;
        
        // 默认打印机名称
        private string? _defaultPrinterName;

        public HardwareService(ILogger<HardwareService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            
            // 获取默认打印机
            _defaultPrinterName = GetDefaultPrinterName();
            _logger.LogInformation("HardwareService initialized with default printer: {PrinterName}", _defaultPrinterName ?? "None");
        }

        #region 打印机操作

        /// <summary>
        /// 获取打印机状态
        /// </summary>
        public async Task<PrinterStatusDto> GetPrinterStatusAsync()
        {
            try
            {
                var printerName = _defaultPrinterName ?? "Unknown";
                var isOnline = IsPrinterOnline(printerName);
                
                return await Task.FromResult(new PrinterStatusDto
                {
                    Connected = isOnline,
                    Status = isOnline ? "online" : "offline",
                    PrinterName = printerName,
                    LastError = null,
                    LastPrintTime = null
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting printer status");
                return new PrinterStatusDto
                {
                    Connected = false,
                    Status = "error",
                    LastError = ex.Message
                };
            }
        }

        /// <summary>
        /// 打印票据
        /// </summary>
        public async Task<PrinterResponseDto> PrintTicketAsync(PrintTicketDto printDto)
        {
            try
            {
                var printerName = string.IsNullOrEmpty(printDto.PrinterName) ? _defaultPrinterName : printDto.PrinterName;
                
                if (string.IsNullOrEmpty(printerName))
                {
                    return new PrinterResponseDto
                    {
                        Success = false,
                        Message = "No printer available",
                        Error = "No default printer found and no printer specified"
                    };
                }

                // 生成打印内容
                string printContent = GeneratePrintContent(printDto);
                
                // 使用RawPrinterHelper打印
                bool success = RawPrinterHelper.SendStringToPrinter(printerName, printContent);
                
                if (success)
                {
                    _logger.LogInformation("Successfully printed {Type} ticket to printer {PrinterName}", printDto.Type, printerName);
                    return new PrinterResponseDto
                    {
                        Success = true,
                        Message = $"Successfully printed {printDto.Type} ticket",
                        Data = new Dictionary<string, object>
                        {
                            ["PrinterName"] = printerName,
                            ["TicketType"] = printDto.Type,
                            ["Timestamp"] = DateTime.UtcNow
                        }
                    };
                }
                else
                {
                    return new PrinterResponseDto
                    {
                        Success = false,
                        Message = "Failed to print ticket",
                        Error = "RawPrinterHelper.SendStringToPrinter returned false"
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error printing ticket");
                return new PrinterResponseDto
                {
                    Success = false,
                    Message = "Error occurred while printing ticket",
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// 测试打印机
        /// </summary>
        public async Task<PrinterResponseDto> TestPrinterAsync()
        {
            try
            {
                var testPrintDto = new PrintTicketDto
                {
                    Type = "test",
                    CustomContent = "打印机测试\nPrinter Test\n" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                    FontName = "Arial",
                    FontSize = 12f
                };

                return await PrintTicketAsync(testPrintDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing printer");
                return new PrinterResponseDto
                {
                    Success = false,
                    Message = "Error occurred while testing printer",
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// 获取可用打印机列表
        /// </summary>
        public async Task<PrinterListResponseDto> GetAvailablePrintersAsync()
        {
            try
            {
                var printers = new List<PrinterInfoDto>();
                
                foreach (string printerName in PrinterSettings.InstalledPrinters)
                {
                    var printerInfo = new PrinterInfoDto
                    {
                        Name = printerName,
                        IsDefault = printerName == _defaultPrinterName,
                        IsOnline = IsPrinterOnline(printerName),
                        Status = IsPrinterOnline(printerName) ? "Ready" : "Offline"
                    };
                    printers.Add(printerInfo);
                }

                return await Task.FromResult(new PrinterListResponseDto
                {
                    Success = true,
                    Message = $"Found {printers.Count} printers",
                    Printers = printers
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting available printers");
                return new PrinterListResponseDto
                {
                    Success = false,
                    Message = "Error occurred while getting available printers",
                    Error = ex.Message,
                    Printers = new List<PrinterInfoDto>()
                };
            }
        }

        #endregion

        #region 钱箱操作

        /// <summary>
        /// 打开钱箱
        /// </summary>
        public async Task<CashDrawerResponseDto> OpenCashDrawerAsync(OpenCashDrawerDto openDto)
        {
            try
            {
                var printerName = string.IsNullOrEmpty(openDto.PrinterName) ? _defaultPrinterName : openDto.PrinterName;
                
                if (string.IsNullOrEmpty(printerName))
                {
                    return new CashDrawerResponseDto
                    {
                        Success = false,
                        Message = "No printer available for cash drawer operation",
                        Error = "No default printer found and no printer specified"
                    };
                }

                // 使用指定的ESC/POS命令或默认命令
                string command = string.IsNullOrEmpty(openDto.Command) ? "\x1B\x70\x00\x32\x32" : openDto.Command;
                
                // 使用RawPrinterHelper发送命令
                bool success = RawPrinterHelper.SendStringToPrinter(printerName, command);
                
                if (success)
                {
                    _logger.LogInformation("Successfully opened cash drawer using printer {PrinterName}. Reason: {Reason}", 
                        printerName, openDto.Reason ?? "Manual operation");
                    
                    return new CashDrawerResponseDto
                    {
                        Success = true,
                        Message = "Cash drawer opened successfully",
                        PrinterUsed = printerName
                    };
                }
                else
                {
                    return new CashDrawerResponseDto
                    {
                        Success = false,
                        Message = "Failed to open cash drawer",
                        Error = "RawPrinterHelper.SendStringToPrinter returned false",
                        PrinterUsed = printerName
                    };
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error opening cash drawer");
                return new CashDrawerResponseDto
                {
                    Success = false,
                    Message = "Error occurred while opening cash drawer",
                    Error = ex.Message
                };
            }
        }

        /// <summary>
        /// 测试钱箱
        /// </summary>
        public async Task<CashDrawerResponseDto> TestCashDrawerAsync()
        {
            try
            {
                var testOpenDto = new OpenCashDrawerDto
                {
                    Reason = "Test operation"
                };

                return await OpenCashDrawerAsync(testOpenDto);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error testing cash drawer");
                return new CashDrawerResponseDto
                {
                    Success = false,
                    Message = "Error occurred while testing cash drawer",
                    Error = ex.Message
                };
            }
        }

        #endregion

        #region 私有辅助方法

        /// <summary>
        /// 获取默认打印机名称
        /// </summary>
        private string? GetDefaultPrinterName()
        {
            try
            {
                var printDocument = new PrintDocument();
                return printDocument.PrinterSettings.PrinterName;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Could not get default printer name");
                return null;
            }
        }

        /// <summary>
        /// 检查打印机是否在线
        /// </summary>
        private bool IsPrinterOnline(string printerName)
        {
            try
            {
                var printerSettings = new PrinterSettings { PrinterName = printerName };
                return printerSettings.IsValid;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 生成打印内容
        /// </summary>
        private string GeneratePrintContent(PrintTicketDto printDto)
        {
            var content = new StringBuilder();
            
            // 如果有自定义内容，直接使用
            if (!string.IsNullOrEmpty(printDto.CustomContent))
            {
                content.AppendLine(printDto.CustomContent);
                return content.ToString();
            }

            // 根据票据类型生成内容
            switch (printDto.Type.ToLower())
            {
                case "entry":
                    content.AppendLine("========== 停车入场票 ==========");
                    content.AppendLine($"车牌号: {printDto.LicensePlate ?? "未识别"}");
                    content.AppendLine($"入场时间: {printDto.Timestamp:yyyy-MM-dd HH:mm:ss}");
                    content.AppendLine("请妥善保管此票据");
                    break;
                    
                case "receipt":
                    content.AppendLine("========== 停车缴费收据 ==========");
                    content.AppendLine($"车牌号: {printDto.LicensePlate ?? "未识别"}");
                    content.AppendLine($"缴费金额: ¥{printDto.Amount:F2}");
                    content.AppendLine($"支付方式: {printDto.PaymentMethod ?? "现金"}");
                    content.AppendLine($"缴费时间: {printDto.Timestamp:yyyy-MM-dd HH:mm:ss}");
                    content.AppendLine("谢谢使用，祝您出行愉快！");
                    break;
                    
                case "test":
                    content.AppendLine("========== 打印机测试 ==========");
                    content.AppendLine("Printer Test");
                    content.AppendLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                    content.AppendLine("如果您能看到此内容，说明打印机工作正常");
                    break;
                    
                default:
                    content.AppendLine("========== 停车系统票据 ==========");
                    content.AppendLine($"票据类型: {printDto.Type}");
                    content.AppendLine($"时间: {printDto.Timestamp:yyyy-MM-dd HH:mm:ss}");
                    break;
            }
            
            content.AppendLine("================================");
            content.AppendLine(); // 空行，便于撕纸
            
            return content.ToString();
        }

        #endregion
    }
}
