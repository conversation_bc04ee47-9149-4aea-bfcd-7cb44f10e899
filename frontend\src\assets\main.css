@import './base.css';

#app {
  width: 100vw;
  height: 100vh;
  margin: 0;
  padding: 0;
  font-weight: normal;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Naive UI 兼容性样式 */
.n-config-provider {
  height: 100%;
}

/* 蓝色主题辅助类 */
.blue-primary {
  color: var(--blue-primary) !important;
}

.blue-bg-primary {
  background-color: var(--blue-primary) !important;
}

.blue-border-primary {
  border-color: var(--blue-primary) !important;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    margin: 0;
    padding: 0;
  }

  #app {
    width: 100vw;
    height: 100vh;
    margin: 0;
    padding: 0;
  }
}
