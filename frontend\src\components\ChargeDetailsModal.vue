<template>
  <!-- 收费明细弹窗 -->
  <NModal
    :show="props.visible"
    preset="card"
    title="當前設定文件明細 (不包含當前上述的收費明細數據明細)"
    size="huge"
    :bordered="false"
    :segmented="true"
    @update:show="handleClose"
  >

      <div class="charge-details-body">
        <!-- 筛选区域 -->
        <div class="filter-section">
          <!-- 日期时间筛选 -->
          <div class="date-filter-row">
            <div class="date-inputs">
              <div class="date-group">
                <label>开始日期:</label>
                <input
                  type="date"
                  v-model="filters.startDate"
                  class="date-input"
                />
              </div>
              <div class="date-group">
                <label>开始时间:</label>
                <input
                  type="time"
                  v-model="filters.startTime"
                  class="time-input"
                />
              </div>
              <div class="date-group">
                <label>结束日期:</label>
                <input
                  type="date"
                  v-model="filters.endDate"
                  class="date-input"
                />
              </div>
              <div class="date-group">
                <label>结束时间:</label>
                <input
                  type="time"
                  v-model="filters.endTime"
                  class="time-input"
                />
              </div>
            </div>
          </div>

          <!-- 条件筛选 -->
          <div class="condition-filter-row">
            <div class="filter-inputs">
              <div class="filter-group">
                <label>车牌号码:</label>
                <input
                  type="text"
                  v-model="filters.plateNumber"
                  placeholder="请输入车牌号"
                  class="filter-input"
                  @keyup.enter="searchChargeDetails"
                />
              </div>
              <div class="filter-group">
                <label>支付方式:</label>
                <select v-model="filters.paymentMethod" class="filter-select">
                  <option value="">全部</option>
                  <option value="1">现金支付</option>
                  <option value="2">微信支付</option>
                  <option value="3">支付宝</option>
                  <option value="4">银行卡</option>
                  <option value="5">月卡</option>
                </select>
              </div>
              <div class="filter-group">
                <label>账单状态:</label>
                <select v-model="filters.billStatus" class="filter-select">
                  <option value="">全部</option>
                  <option value="0">待支付</option>
                  <option value="1">已完成</option>
                  <option value="2">已取消</option>
                  <option value="3">进行中</option>
                </select>
              </div>
              <div class="filter-group">
                <label>收费类型:</label>
                <select v-model="filters.chargeType" class="filter-select">
                  <option value="">全部</option>
                  <option value="0">无优惠</option>
                  <option value="1">免费</option>
                  <option value="2">打折</option>
                  <option value="3">减免</option>
                </select>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="filter-actions">
            <NSpace>
              <NButton
                type="primary"
                @click="searchChargeDetails"
                :loading="loading"
                size="medium"
              >
                搜索
              </NButton>
              <NButton
                type="default"
                @click="resetFilters"
                :disabled="loading"
                size="medium"
              >
                重置
              </NButton>
              <NButton
                type="info"
                @click="exportChargeDetails"
                :disabled="loading"
                size="medium"
              >
                导出
              </NButton>
            </NSpace>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-state">
          <div class="loading-spinner"></div>
          <p>正在加载收费明细...</p>
        </div>

        <!-- 错误状态 -->
        <div v-else-if="error" class="error-state">
          <p class="error-message">{{ error }}</p>
          <button class="retry-btn" @click="loadChargeDetails">重试</button>
        </div>

        <!-- 收费明细表格 -->
        <div v-else class="charge-details-table">
          <div class="table-header">
            <div class="header-cell plate-number">车牌号码</div>
            <div class="header-cell amount">应收金额</div>
            <div class="header-cell amount">实收金额</div>
            <div class="header-cell amount">优惠金额</div>
            <div class="header-cell discount">优惠类型</div>
            <div class="header-cell datetime">入场时间</div>
            <div class="header-cell datetime">出场时间</div>
            <div class="header-cell payment">付款方式</div>
            <div class="header-cell operator">操作员</div>
            <div class="header-cell status">状态</div>
            <div class="header-cell action">操作</div>
          </div>

          <div class="table-body">
            <!-- 空数据状态 -->
            <div v-if="!chargeData.records || chargeData.records.length === 0" class="empty-state">
              <p>暂无收费明细数据</p>
            </div>
            
            <!-- 数据行 -->
            <div
              v-else
              v-for="record in chargeData.records"
              :key="record.id"
              class="table-row"
            >
              <div class="table-cell plate-number">
                {{ record.carNum || '--' }}
              </div>
              <div class="table-cell amount">{{ formatAmount(record.receivableFee) }}</div>
              <div class="table-cell amount">{{ formatAmount(record.fee) }}</div>
              <div class="table-cell amount">{{ formatAmount(record.discountAmount) }}</div>
              <div class="table-cell discount">{{ getDiscountTypeName(record.chargeType) }}</div>
              <div class="table-cell datetime">{{ formatDateTime(record.enterTime) }}</div>
              <div class="table-cell datetime">{{ formatDateTime(record.leaveTime) }}</div>
              <div class="table-cell payment">{{ getPaymentMethodName(record.payType) }}</div>
              <div class="table-cell operator">{{ record.chargePersonnel || '--' }}</div>
              <div class="table-cell status">
                <span class="status-badge" :class="getStatusColor(record.status)">
                  {{ getStatusName(record.billStatus) }}
                </span>
              </div>
              <div class="table-cell action">
                <button class="action-link" @click="viewDetails(record)">详情</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页和统计信息 -->
        <div v-if="!loading && !error" class="charge-footer">
          <!-- 统计信息 -->
          <div class="charge-statistics">
            <div class="stats-item">
              <span class="stats-label">总计 {{ chargeData.pagination?.total || 0 }}条</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">应收金额: {{ formatAmount(chargeData.statistics?.totalReceivable || 0) }}</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">实收金额: {{ formatAmount(chargeData.statistics?.totalActual || 0) }}</span>
            </div>
            <div class="stats-item">
              <span class="stats-label">优惠金额: {{ formatAmount(chargeData.statistics?.totalDiscount || 0) }}</span>
            </div>
          </div>

          <!-- 分页控件 -->
          <div v-if="chargeData.pagination && chargeData.pagination.total > 0" class="pagination">
            <NSpace align="center">
              <NButton
                type="default"
                :disabled="chargeData.pagination.pageNum <= 1"
                @click="changePage(chargeData.pagination.pageNum - 1)"
                size="small"
              >
                上一页
              </NButton>
              <NText class="page-info">
                第 {{ chargeData.pagination.pageNum }} 页 / 共 {{ chargeData.pagination.pages }} 页
              </NText>
              <NButton
                type="default"
                :disabled="chargeData.pagination.pageNum >= chargeData.pagination.pages"
                @click="changePage(chargeData.pagination.pageNum + 1)"
                size="small"
              >
                下一页
              </NButton>
            </NSpace>
          </div>
        </div>
      </div>
  </NModal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, onMounted, onUnmounted } from 'vue'
import {
  NModal,
  NCard,
  NInput,
  NDatePicker,
  NTimePicker,
  NSelect,
  NButton,
  NTable,
  NSpace,
  NGrid,
  NGridItem,
  NText,
  NTag,
  useMessage,
  useNotification
} from 'naive-ui'
import httpClient from '@/services/httpClient'
import { debounce } from '@/utils/helpers'

// Naive UI 钩子
const message = useMessage()
const naiveNotification = useNotification()

// Props
interface Props {
  visible: boolean
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  close: []
}>()

// 性能优化相关代码已移除

// 响应式数据
const loading = ref(false)
const error = ref('')

// 筛选条件
const filters = reactive({
  startDate: '',
  startTime: '',
  endDate: '',
  endTime: '',
  plateNumber: '', // 车牌号
  paymentMethod: '', // 支付方式
  billStatus: '', // 账单状态
  chargeType: '', // 收费类型
  pageNum: 1,
  pageSize: 10
})

// 收费数据
const chargeData = reactive({
  records: [] as any[],
  pagination: null as any,
  statistics: {
    totalReceivable: 0,
    totalActual: 0,
    totalDiscount: 0
  }
})

// 初始化默认日期
const initDefaultDates = () => {
  const today = new Date()
  const dateStr = today.toISOString().split('T')[0]

  filters.startDate = dateStr
  filters.endDate = dateStr
  filters.startTime = '00:00'
  filters.endTime = '23:59'
}

// 组件挂载时初始化
onMounted(() => {
  initDefaultDates()
})

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    loadChargeDetails()
  }
})

// 关闭弹窗
const handleClose = (value?: boolean) => {
  if (value === false || value === undefined) {
    emit('update:visible', false)
    emit('close')
  }
}

// 加载收费明细数据（优化版本）
const loadChargeDetails = async () => {
  loading.value = true
  error.value = ''

  try {
    // 构建符合 ChargeRecordVo 格式的请求参数
    const params = {
      startTime: `${filters.startDate} ${filters.startTime}:00`,
      endTime: `${filters.endDate} ${filters.endTime}:59`,
      pageNum: filters.pageNum,
      pageSize: filters.pageSize,
      selDateType: 1, // 按时间查询
      carNum: filters.plateNumber || '', // 车牌号
      payType: filters.paymentMethod ? parseInt(filters.paymentMethod) : null, // 支付方式
      chargeType: filters.chargeType ? parseInt(filters.chargeType) : null, // 收费类型
      billStatus: filters.billStatus ? parseInt(filters.billStatus) : null // 账单状态
    }

    console.log('请求参数:', params)

    // 直接调用 httpClient，因为现有的 parkingApi.getChargeDetails 参数格式不匹配
    const response = await httpClient.post('/report/getChargeInfo', params)

    console.log('API响应:', response)

    if (response.status === 1 && response.data) {
      // 根据 PageResultResponse<ChargeRecordVo> 格式解析数据
      const pageData = response.data
      chargeData.records = pageData.list || []
      chargeData.pagination = {
        pageNum: pageData.pageNum || 1,
        pageSize: pageData.pageSize || 10,
        total: pageData.total || 0,
        pages: pageData.pages || 1,
        hasNextPage: pageData.hasNextPage || false,
        hasPreviousPage: pageData.hasPreviousPage || false
      }

      // 计算统计信息
      calculateStatistics()

      // 显示成功提示（仅在搜索时显示）
      if (filters.pageNum === 1) {
        const total = chargeData.pagination.total
        message.success(`成功加载 ${total} 条收费明细记录`)
      }
    } else {
      const errorMsg = response.msg || '获取收费明细失败'
      error.value = errorMsg
      message.error(errorMsg)
    }
  } catch (err: any) {
    const errorMsg = err.message || '网络请求失败，请检查网络连接'
    error.value = errorMsg
    message.error(errorMsg)
    console.error('获取收费明细失败:', err)
  } finally {
    loading.value = false
  }
}

// 计算统计信息
const calculateStatistics = () => {
  const stats = chargeData.records.reduce((acc, record) => {
    acc.totalReceivable += record.receivableFee || 0
    acc.totalActual += record.fee || 0
    acc.totalDiscount += record.discountAmount || 0
    return acc
  }, { totalReceivable: 0, totalActual: 0, totalDiscount: 0 })
  
  chargeData.statistics = stats
}

// 防抖搜索函数
const debouncedSearch = debounce(() => {
  loadChargeDetails()
}, 300)

// 搜索收费明细
const searchChargeDetails = () => {
  // 验证日期范围
  if (!filters.startDate || !filters.endDate) {
    message.warning('请选择查询日期范围')
    return
  }

  const startDateTime = new Date(`${filters.startDate} ${filters.startTime}`)
  const endDateTime = new Date(`${filters.endDate} ${filters.endTime}`)

  if (startDateTime > endDateTime) {
    message.warning('开始时间不能晚于结束时间')
    return
  }

  // 检查日期范围是否过大（超过3个月）
  const diffTime = endDateTime.getTime() - startDateTime.getTime()
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays > 90) {
    message.warning('查询时间范围不能超过3个月')
    return
  }

  filters.pageNum = 1 // 重置到第一页
  debouncedSearch() // 使用防抖搜索
}

// 重置筛选条件
const resetFilters = () => {
  // 保留日期时间，重置其他筛选条件
  filters.plateNumber = ''
  filters.paymentMethod = ''
  filters.billStatus = ''
  filters.chargeType = ''
  filters.pageNum = 1

  message.success('筛选条件已重置')

  // 重新加载数据
  loadChargeDetails()
}

// 导出收费明细
const exportChargeDetails = () => {
  try {
    // 检查是否有数据可导出
    if (!chargeData.records || chargeData.records.length === 0) {
      message.warning('暂无数据可导出')
      return
    }

    // 构建导出参数，与搜索参数相同
    const params = {
      startTime: `${filters.startDate} ${filters.startTime}:00`,
      endTime: `${filters.endDate} ${filters.endTime}:59`,
      carNum: filters.plateNumber || '',
      payType: filters.paymentMethod ? parseInt(filters.paymentMethod) : null,
      chargeType: filters.chargeType ? parseInt(filters.chargeType) : null,
      billStatus: filters.billStatus ? parseInt(filters.billStatus) : null,
      selDateType: 1
    }

    // 使用 window.open 打开导出链接
    const baseUrl = httpClient.getBaseURL()
    const queryParams = new URLSearchParams()

    // 添加参数
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        queryParams.append(key, value.toString())
      }
    })

    // 添加认证令牌
    const token = localStorage.getItem('parking_booth_token')
    if (token) {
      queryParams.append('accessToken', token)
    } else {
      message.error('未找到认证令牌，请重新登录')
      return
    }

    // 打开导出链接
    const exportUrl = `${baseUrl}/report/getAllChargeInfo?${queryParams.toString()}`
    window.open(exportUrl, '_blank')

    message.success('正在导出收费明细，请稍候...')
    console.log('导出收费明细:', exportUrl)
  } catch (err: any) {
    const errorMsg = '导出失败: ' + (err.message || '未知错误')
    message.error(errorMsg)
    console.error('导出收费明细失败:', err)
  }
}

// 分页切换
const changePage = (page: number) => {
  filters.pageNum = page
  loadChargeDetails()
}

// 查看详情
const viewDetails = (record: any) => {
  console.log('查看详情:', record)
}

// 格式化金额
const formatAmount = (amount: number | string) => {
  if (!amount && amount !== 0) return '0MOP'
  const num = typeof amount === 'string' ? parseFloat(amount) : amount
  return `${num}MOP`
}

// 格式化日期时间
const formatDateTime = (dateStr: string) => {
  if (!dateStr) return '--'
  return dateStr.replace('T', ' ').slice(0, 19)
}

// 获取优惠类型名称
const getDiscountTypeName = (type: number) => {
  const types: Record<number, string> = {
    0: '无优惠',
    1: '免费',
    2: '打折',
    3: '减免'
  }
  return types[type] || '未知'
}

// 获取支付方式名称
const getPaymentMethodName = (type: number) => {
  const methods: Record<number, string> = {
    1: '现金支付',
    2: '微信支付',
    3: '支付宝',
    4: '银行卡',
    5: '月卡'
  }
  return methods[type] || '未知'
}

// 获取状态名称
const getStatusName = (status: number) => {
  const statuses: Record<number, string> = {
    0: '待支付',
    1: '已完成',
    2: '已取消',
    3: '进行中'
  }
  return statuses[status] || '未知'
}

// 获取状态颜色
const getStatusColor = (status: string | number) => {
  const statusNum = typeof status === 'string' ? parseInt(status) : status
  switch (statusNum) {
    case 1:
      return 'success'
    case 3:
      return 'warning'
    case 2:
      return 'danger'
    default:
      return 'info'
  }
}
</script>

<style scoped>
/* 收费明细弹窗样式 */
.charge-details-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(44, 62, 80, 0.75);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 200000;
  animation: fadeIn 0.3s ease-out;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.charge-details-modal {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  min-width: 1200px;
  max-width: 1400px;
  max-height: 90vh;
  overflow: hidden;
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e1e5e9;
}

.charge-details-header {
  background: #f8f9fa;
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.charge-details-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
}

.close-btn {
  background: none;
  border: none;
  font-size: 20px;
  color: #6c757d;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

.charge-details-body {
  padding: 24px;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

/* 筛选区域 */
.filter-section {
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.date-filter-row {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #dee2e6;
}

.condition-filter-row {
  margin-bottom: 16px;
}

.date-inputs {
  display: flex;
  align-items: center;
  gap: 16px;
}

.date-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.date-group label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  min-width: 70px;
}

.date-input,
.time-input {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
}

.date-input:focus,
.time-input:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.date-input {
  width: 140px;
}

.time-input {
  width: 100px;
}

.filter-inputs {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.filter-group label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
  min-width: 70px;
}

.filter-input,
.filter-select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  min-width: 120px;
}

.filter-input:focus,
.filter-select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-top: 16px;
  justify-content: flex-end;
}

.filter-btn {
  padding: 8px 16px;
  border: 1px solid #007bff;
  border-radius: 4px;
  background: #ffffff;
  color: #007bff;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 80px;
}

.filter-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.filter-btn:hover:not(:disabled) {
  background: #007bff;
  color: #ffffff;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.filter-btn.reset {
  border-color: #6c757d;
  color: #6c757d;
}

.filter-btn.reset:hover:not(:disabled) {
  background: #6c757d;
  color: #ffffff;
  border-color: #6c757d;
  box-shadow: 0 2px 8px rgba(108, 117, 125, 0.3);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 错误状态 */
.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #dc3545;
}

.error-message {
  margin-bottom: 16px;
  font-size: 16px;
}

.retry-btn {
  padding: 8px 16px;
  background: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.retry-btn:hover {
  background: #0056b3;
}

/* 空数据状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #6c757d;
  font-size: 16px;
}

/* 表格样式 */
.charge-details-table {
  border: 1px solid #dee2e6;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 20px;
}

.table-header {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.header-cell {
  padding: 12px 8px;
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  flex-shrink: 0;
}

.header-cell:last-child {
  border-right: none;
}

.header-cell.plate-number {
  width: 100px;
}

.header-cell.amount {
  width: 80px;
}

.header-cell.discount {
  width: 80px;
}

.header-cell.datetime {
  width: 140px;
}

.header-cell.payment {
  width: 80px;
}

.header-cell.operator {
  width: 80px;
}

.header-cell.status {
  width: 80px;
}

.header-cell.action {
  width: 60px;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  padding: 12px 8px;
  font-size: 13px;
  color: #495057;
  text-align: center;
  border-right: 1px solid #dee2e6;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  word-break: break-all;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.plate-number {
  width: 100px;
  font-weight: 600;
  color: #2c3e50;
}

.table-cell.amount {
  width: 80px;
  font-weight: 500;
  color: #28a745;
}

.table-cell.discount {
  width: 80px;
}

.table-cell.datetime {
  width: 140px;
  font-size: 12px;
}

.table-cell.payment {
  width: 80px;
}

.table-cell.operator {
  width: 80px;
}

.table-cell.status {
  width: 80px;
}

.table-cell.action {
  width: 60px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.success {
  background: #d4edda;
  color: #155724;
}

.status-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.status-badge.danger {
  background: #f8d7da;
  color: #721c24;
}

.status-badge.info {
  background: #d1ecf1;
  color: #0c5460;
}

.action-link {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  font-size: 12px;
  text-decoration: underline;
  transition: color 0.3s ease;
}

.action-link:hover {
  color: #0056b3;
}

/* 底部区域 */
.charge-footer {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #dee2e6;
}

/* 统计信息 */
.charge-statistics {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e1e5e9;
}

.stats-item {
  display: flex;
  align-items: center;
}

.stats-label {
  font-size: 14px;
  color: #495057;
  font-weight: 500;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
}

.page-btn {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #ffffff;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.page-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f8f9fa;
}

.page-btn:hover:not(:disabled) {
  background: #007bff;
  color: #ffffff;
  border-color: #007bff;
}

.page-info {
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOutRight {
  from {
    opacity: 1;
    transform: translateX(0);
  }
  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .charge-details-modal {
    min-width: 320px;
    margin: 0 20px;
    max-height: 95vh;
  }

  .charge-details-header {
    padding: 16px 20px;
  }

  .charge-details-body {
    padding: 20px;
  }

  .filter-section {
    padding: 16px;
  }

  .date-inputs {
    flex-direction: column;
    gap: 12px;
  }

  .filter-inputs {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .filter-group {
    flex-direction: column;
    align-items: stretch;
    gap: 4px;
  }

  .filter-group label {
    min-width: auto;
  }

  .filter-actions {
    justify-content: center;
    margin-top: 20px;
  }

  .charge-details-table {
    overflow-x: auto;
  }

  .table-header,
  .table-row {
    min-width: 800px;
  }

  .charge-statistics {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .stats-item {
    justify-content: center;
  }
}
</style>
