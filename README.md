# 停車場收費管理系統 (Parking Fee Payment System)

一個基於 Vue 3 + .NET 9 的智慧停車場收費管理系統，支援硬體整合和實時通訊。

## 系統特色

### 🚗 停車場管理
- 車輛進出記錄管理
- 自動計費系統
- 多種付款方式支援（現金、信用卡、澳門通）
- 實時車位狀態監控

### 🔧 硬體整合
- **熱敏印表機**：自動列印停車票據和收據
- **IP攝影機**：車牌識別和影像記錄
- **澳門通讀卡器**：支援澳門通付款
- **地感應器**：自動檢測車輛進出

### 📊 數據分析
- 即時統計報表
- 收入分析
- 車流量統計
- 歷史數據查詢

### 🔄 實時通訊
- SignalR 實時數據推送
- 硬體狀態即時監控
- 系統通知推送

## 技術架構

![系統架構流程圖](docs/flowchart.png)

### 前端 (Frontend)
- **Vue 3** + **TypeScript** + **Vite**
- **Naive UI** 組件庫
- **Pinia** 狀態管理
- **Vue Router** 路由管理
- **SignalR Client** 實時通訊
- **Electron** 桌面應用支援
- 響應式設計，支援觸控操作

### 後端 (Backend)
- **.NET 9** Web API
- **SignalR** 實時通訊
- **Swagger** API 文檔
- **依賴注入** 架構
- **結構化日誌**

## 快速開始

### 系統需求
- Node.js 18+
- .NET 9 SDK
- Windows 10/11 或 Linux

### 安裝步驟

1. **克隆專案**
```bash
git clone <repository-url>
cd ParkingfeePaymentSystem
```

2. **安裝前端依賴**
```bash
cd frontend
npm install
```

3. **啟動後端服務**
```bash
cd ../backend
dotnet run
```

4. **啟動前端開發服務器**
```bash
cd ../frontend
npm run dev
```

5. **訪問系統**
- 前端：http://localhost:5173
- 後端 API：http://localhost:5000
- Swagger 文檔：http://localhost:5000/swagger

### 預設登錄帳號

| 用戶類型 | 用戶名 | 密碼 | 權限 |
|---------|--------|------|------|
| 系統管理員 | admin | admin123 | 完整權限 |
| 操作員 | operator | operator123 | 停車操作、硬體控制 |
| 查看員 | viewer | viewer123 | 僅查看權限 |

## 主要功能

### 1. 登錄系統 ✅
- 多語言支援（繁體中文、簡體中文、英文）
- 角色權限管理
- 自動登錄狀態保持
- **預設進入為登錄頁面**

### 2. SignalR 實時通訊 ✅
- 硬體狀態實時更新
- 車輛檢測事件推送
- 付款完成通知
- 系統通知推送
- **前後端完整整合**

### 3. Swagger API 文檔 ✅
- 完整的 API 文檔
- 互動式 API 測試
- 請求/響應示例
- **訪問地址：http://localhost:5000/swagger**

### 4. 停車操作
- 車輛進場登記
- 車輛出場處理
- 費用自動計算
- 車輛搜索功能

### 5. 付款處理
- 現金付款
- 信用卡付款
- 澳門通付款
- 付款狀態追蹤

### 6. 硬體監控
- 設備狀態監控
- 硬體診斷測試
- 設備重置功能
- 錯誤處理和恢復

### 7. 報表分析
- 日報、週報、月報
- 收入統計圖表
- 車輛類型分析
- 數據導出功能

### 8. 系統設置
- 停車費率配置
- 硬體參數設置
- 系統維護功能

## API 文檔

### 主要端點

#### 停車操作
- `POST /api/parking/entry` - 記錄車輛進場
- `POST /api/parking/exit` - 記錄車輛出場
- `GET /api/parking/current` - 獲取當前停車車輛
- `POST /api/parking/payment` - 處理付款

#### 硬體控制
- `GET /api/hardware/status` - 獲取硬體狀態
- `POST /api/hardware/printer/print` - 列印票據
- `POST /api/hardware/camera/capture` - 拍攝照片
- `POST /api/hardware/cardreader/read` - 讀取卡片

#### 統計報表
- `GET /api/parking/stats/today` - 今日統計
- `GET /api/parking/reports/{type}` - 生成報表

**完整 API 文檔請訪問：http://localhost:5000/swagger**

## SignalR 實時通訊

### 連接端點
- **Hub URL**: `/hubs/parking`

### 主要事件
- `HardwareStatusUpdate` - 硬體狀態更新
- `VehicleDetection` - 車輛檢測事件
- `PaymentCompleted` - 付款完成通知
- `SystemNotification` - 系統通知

### 客戶端方法
- `JoinBoothGroup(boothId)` - 加入停車場群組
- `SendHardwareCommand(command)` - 發送硬體命令
- `Ping()` - 連接測試

## 開發指南

### 前端開發
```bash
cd frontend
npm run dev              # 開發服務器
npm run build            # 生產構建
npm run type-check       # 類型檢查
npm run electron:smart   # Electron 桌面應用
```

### 後端開發
```bash
cd backend
dotnet run           # 啟動開發服務器
dotnet build         # 構建項目
dotnet test          # 運行測試
```

## 項目結構

```
ParkingfeePaymentSystem/
├── frontend/                    # Vue 3 前端應用
│   ├── src/
│   │   ├── components/         # 可重用組件
│   │   │   ├── TopMenuBar.vue  # 頂部導航欄
│   │   │   ├── ChargeDetailsModal.vue # 收費明細組件
│   │   │   └── NotificationSystem.vue # 通知系統
│   │   ├── views/             # 頁面組件
│   │   │   ├── LoginView.vue  # 登錄頁面 ✅
│   │   │   └── DashboardView.vue # 主控制台
│   │   ├── stores/            # Pinia 狀態管理
│   │   │   └── auth.ts        # 認證狀態管理 ✅
│   │   ├── services/          # API 服務
│   │   │   ├── httpClient.ts  # HTTP 客戶端
│   │   │   ├── authApi.ts     # 認證 API
│   │   │   └── parkingApi.ts  # 停車場 API
│   │   ├── theme/             # UI 主題配置
│   │   │   └── blue-theme.ts  # 藍色主題
│   │   ├── utils/             # 工具函數
│   │   └── router/            # 路由配置
│   ├── electron/              # Electron 桌面應用
│   └── dist/                  # 構建輸出
├── backend/                    # .NET 9 後端 API
│   ├── Controllers/           # API 控制器
│   │   ├── ParkingController.cs
│   │   ├── HardwareController.cs
│   │   ├── EntranceController.cs
│   │   └── ExitController.cs
│   ├── Services/              # 業務邏輯服務
│   │   ├── SignalRNotificationService.cs # SignalR 通知服務 ✅
│   │   ├── ParkingService.cs
│   │   ├── HardwareService.cs
│   │   ├── EntranceService.cs
│   │   └── ExitService.cs
│   ├── Hubs/                  # SignalR 集線器
│   │   └── ParkingHub.cs      # 停車場 Hub ✅
│   ├── Models/                # 數據模型
│   ├── DTOs/                  # 數據傳輸對象
│   └── Interfaces/            # 服務接口
├── docs/                      # 項目文檔
│   └── flowchart.png          # 系統架構流程圖
└── README.md                  # 項目說明文檔
```

## 已完成功能 ✅

1. **登錄系統**
   - ✅ 預設進入登錄頁面
   - ✅ 多語言支援
   - ✅ 角色權限管理
   - ✅ 自動登錄狀態保持

2. **SignalR 實時通訊**
   - ✅ 前後端 SignalR 整合
   - ✅ 硬體狀態實時推送
   - ✅ 車輛檢測事件
   - ✅ 系統通知機制

3. **Swagger API 文檔**
   - ✅ 完整的 API 文檔
   - ✅ 互動式測試界面
   - ✅ 詳細的請求/響應說明

## 技術亮點

- **TypeScript** 全面支援，提供更好的開發體驗
- **Naive UI** 現代化組件庫，美觀且功能豐富
- **Electron** 跨平台桌面應用支援
- **響應式設計**，適配各種屏幕尺寸
- **實時通訊**，硬體狀態即時更新
- **模組化架構**，易於維護和擴展
- **完整的 API 文檔**，便於前後端協作
- **性能優化**，流暢的用戶體驗

## 許可證

本項目採用 MIT 許可證

## 支援

- 📧 技術支援：<EMAIL>
- 📞 緊急熱線：+853-1234-5678
- 🐛 問題報告：GitHub Issues

---

**泓睿股份有限公司** © 2025
