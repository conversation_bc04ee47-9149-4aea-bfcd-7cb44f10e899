# 岗亭批量修改车牌弹框

## 功能概述

岗亭批量修改车牌弹框是一个用于批量修改车牌信息的组件，支持搜索、筛选、选择和批量修改车牌号码。

## 功能特性

### 1. 搜索和筛选
- **车牌号码搜索**: 支持按车牌号码模糊搜索
- **时间范围筛选**: 支持按时间范围筛选车牌记录
- **车牌颜色筛选**: 支持按车牌颜色（蓝牌、黄牌、绿牌、白牌）筛选
- **入口通道筛选**: 支持按入口通道筛选
- **空车牌筛选**: 支持筛选包含空车牌的记录

### 2. 车牌展示
- **网格布局**: 以网格形式展示车牌图片和信息
- **图片预览**: 显示车牌抓拍图片，无图片时显示占位符
- **车牌信息**: 显示车牌号码、抓拍时间等信息
- **选择状态**: 支持多选车牌，选中状态有视觉反馈

### 3. 批量操作
- **多选功能**: 支持点击选择/取消选择车牌
- **批量修改**: 支持对选中的车牌进行批量修改
- **单个修改**: 支持对单个车牌进行修改

### 4. 分页功能
- **分页显示**: 支持分页显示车牌列表
- **页面大小**: 支持调整每页显示数量（10、20、50、100）
- **总数统计**: 显示总记录数和当前页信息

## 组件接口

### Props
```typescript
interface Props {
  visible: boolean  // 弹框显示状态
}
```

### Events
```typescript
interface Emits {
  (e: 'update:visible', value: boolean): void  // 更新显示状态
  (e: 'batchEdit', data: { plateIds: number[]; newPlateNumber: string }): void  // 批量修改事件
}
```

## 使用方法

### 1. 在组件中导入
```vue
<script setup>
import { BatchPlateEditModal } from '@/views/model'
</script>
```

### 2. 在模板中使用
```vue
<template>
  <BatchPlateEditModal
    :visible="showBatchPlateEditModal"
    @update:visible="showBatchPlateEditModal = $event"
    @batchEdit="handleBatchPlateEdit"
  />
</template>
```

### 3. 在脚本中处理事件
```vue
<script setup>
const showBatchPlateEditModal = ref(false)

const handleBatchPlateEdit = (data) => {
  console.log('批量修改车牌:', data)
  // 处理批量修改逻辑
}
</script>
```

## 操作流程

### 1. 打开弹框
点击"岗亭批量修改车牌"按钮打开弹框

### 2. 搜索筛选
- 输入车牌号码进行搜索
- 选择时间范围筛选记录
- 选择车牌颜色和入口通道
- 勾选是否包含空车牌
- 点击"搜索"按钮执行搜索

### 3. 选择车牌
- 点击车牌项进行选择/取消选择
- 选中的车牌会有蓝色边框和背景色
- 底部显示已选择的车牌数量

### 4. 批量修改
- 选择需要修改的车牌
- 点击"批量修改"按钮
- 系统会触发批量修改事件

### 5. 单个修改
- 点击单个车牌的"修改"按钮
- 可以对单个车牌进行修改

## 样式特性

- **响应式设计**: 支持不同屏幕尺寸
- **网格布局**: 自适应网格布局，最小宽度200px
- **视觉反馈**: 选中状态、悬停效果等
- **加载状态**: 搜索和保存时的加载状态
- **分页控件**: 完整的分页功能

## 数据格式

### 车牌数据结构
```typescript
interface PlateData {
  id: number
  plateNumber: string
  imageUrl?: string
  captureTime: Date
  plateColor: string
  channel: string
}
```

### 搜索条件
```typescript
interface SearchForm {
  plateNumber: string
  dateRange: [number, number] | null
  plateColor: string
  entryChannel: string
  includeEmpty: boolean
}
```

## 注意事项

1. **图片加载**: 车牌图片可能加载失败，组件会显示占位符
2. **时间格式**: 时间显示格式为 "yyyy-MM-dd HH:mm"
3. **选择限制**: 建议限制单次选择的车牌数量，避免性能问题
4. **网络请求**: 搜索和修改操作需要处理网络请求的错误情况
5. **权限控制**: 批量修改功能需要相应的权限验证

## 扩展功能

可以根据需要扩展以下功能：
- 导出选中车牌数据
- 批量删除车牌记录
- 车牌识别结果对比
- 修改历史记录
- 批量导入车牌数据
