<template>
  <!-- 详情弹框 -->
  <NModal
    :show="visible"
    preset="card"
    title="详情(显示完整的单次数据，包含该车辆线上支付、提前缴费等)"
    size="huge"
    :mask-closable="true"
    :closable="true"
    style="width: 90vw; height: 85vh;"
    @update:show="handleUpdateVisible"
  >
    <div v-if="record" class="detail-modal-content">
      <!-- 车辆基本信息和图片 -->
      <div class="vehicle-info-section">
        <div class="vehicle-basic-info">
          <div class="info-row">
            <span class="info-label">车牌号码</span>
            <span class="info-value">{{ record.plateNumber || '--' }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">逗留时长</span>
            <span class="info-value">{{ calculateDuration(record) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">车辆类型</span>
            <span class="info-value">{{ getVehicleType(record) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">车辆类型</span>
            <span class="info-value">{{ getVehicleCategory(record) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">停车时长</span>
            <span class="info-value">{{ calculateParkingDuration(record) }}</span>
          </div>
          <div class="info-row">
            <span class="info-label">应收金额</span>
            <span class="info-value">{{ record.totalAmount }}</span>
          </div>
        </div>

        <div class="vehicle-images">
          <div class="image-container">
            <h4>入场图片</h4>
            <NImage
              :src="`https://picsum.photos/400/300?random=${record.id}`"
              alt="入场图片"
              width="400"
              height="300"
              object-fit="cover"
              :preview-disabled="false"
              show-toolbar-tooltip
            />
          </div>
          <div class="image-container">
            <h4>出场图片</h4>
            <NImage
              :src="`https://picsum.photos/400/300?random=${record.id + 100}`"
              alt="出场图片"
              width="400"
              height="300"
              object-fit="cover"
              :preview-disabled="false"
              show-toolbar-tooltip
            />
          </div>
        </div>
      </div>

      <!-- 账单详情 -->
      <div class="bill-details-section">
        <h3>账单详情</h3>
        <NDataTable
          :columns="billColumns"
          :data="billDetails"
          size="small"
          :pagination="false"
          striped
        />
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  NModal,
  NButton,
  NSpace,
  NDataTable,
  NImage,
  type DataTableColumns
} from 'naive-ui'

defineOptions({
  name: 'DetailModal'
})

// ==================== 接口定义 ====================
interface EntryDataRecord {
  id: number
  plateNumber: string
  totalAmount: string
  actualAmount: string
  freeAmount: string
  actualDeduction: string
  entryTime: string | null
  entryLocation: string
  exitLocation: string
  exitTime: string
  releaseType: string
  status: string
}

interface BillDetail {
  id: number
  paymentType: string
  paymentMethod: string
  totalAmount: string
  freeAmount: string
  discountAmount: string
  actualAmount: string
  parkingDuration: string
  remarks: string
  paymentMethod2: string
  paymentTime: string
}

// ==================== Props 和 Emits ====================
interface Props {
  visible: boolean
  record: EntryDataRecord | null
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// ==================== 账单详情表格列定义 ====================
const billColumns: DataTableColumns<BillDetail> = [
  {
    title: '支付类型',
    key: 'paymentType',
    width: 100,
    align: 'center'
  },
  {
    title: '支付管道',
    key: 'paymentMethod',
    width: 100,
    align: 'center'
  },
  {
    title: '费收金额',
    key: 'totalAmount',
    width: 100,
    align: 'center'
  },
  {
    title: '优惠金额',
    key: 'freeAmount',
    width: 100,
    align: 'center'
  },
  {
    title: '免费金额',
    key: 'discountAmount',
    width: 100,
    align: 'center'
  },
  {
    title: '抵扣金额',
    key: 'actualAmount',
    width: 100,
    align: 'center'
  },
  {
    title: '抵扣时间(小时)',
    key: 'parkingDuration',
    width: 120,
    align: 'center'
  },
  {
    title: '费率批注',
    key: 'remarks',
    width: 100,
    align: 'center'
  },
  {
    title: '支付方式',
    key: 'paymentMethod2',
    width: 100,
    align: 'center'
  },
  {
    title: '缴费时间',
    key: 'paymentTime',
    width: 150,
    align: 'center'
  }
]

// 账单详情数据
const billDetails = ref<BillDetail[]>([
  {
    id: 1,
    paymentType: '停车费',
    paymentMethod: '停车收费车',
    totalAmount: '19.4MOP',
    freeAmount: '0MOP',
    discountAmount: '0MOP',
    actualAmount: '0MOP',
    parkingDuration: '0.00',
    remarks: '0MOP',
    paymentMethod2: '现金',
    paymentTime: '2025-08-01 11:37:50'
  }
])

// ==================== 方法定义 ====================
/**
 * 处理弹框显示状态更新
 */
const handleUpdateVisible = (visible: boolean) => {
  emit('update:visible', visible)
}

/**
 * 计算逗留时长
 */
const calculateDuration = (record: EntryDataRecord) => {
  if (!record.entryTime) return '--'
  const entryTime = new Date(record.entryTime)
  const exitTime = new Date(record.exitTime)
  const duration = exitTime.getTime() - entryTime.getTime()
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
  return `${hours}小时${minutes}分钟`
}

/**
 * 获取车辆类型
 */
const getVehicleType = (record: EntryDataRecord) => {
  if (record.entryLocation.includes('电单车') || record.exitLocation.includes('电单车')) {
    return '电单车'
  }
  return '小型车'
}

/**
 * 获取车辆分类
 */
const getVehicleCategory = (record: EntryDataRecord) => {
  return getVehicleType(record)
}

/**
 * 计算停车时长
 */
const calculateParkingDuration = (record: EntryDataRecord) => {
  if (!record.entryTime) return '--'
  const entryTime = new Date(record.entryTime)
  const exitTime = new Date(record.exitTime)
  const duration = exitTime.getTime() - entryTime.getTime()
  const hours = Math.floor(duration / (1000 * 60 * 60))
  const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60))
  return `${hours}时${minutes}分${Math.floor((duration % (1000 * 60)) / 1000)}秒`
}
</script>

<style scoped>
/* 详情弹框样式 */
.detail-modal-content {
  padding: 20px;
}

.vehicle-info-section {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.vehicle-basic-info {
  flex: 1;
  min-width: 300px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  padding: 8px 0;
}

.info-label {
  font-weight: 600;
  color: #333;
  min-width: 80px;
  margin-right: 16px;
}

.info-value {
  color: #666;
  font-size: 14px;
}

.vehicle-images {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.image-container {
  text-align: center;
}

.image-container h4 {
  margin: 0 0 10px 0;
  color: #333;
  font-size: 14px;
  font-weight: 600;
}

.image-container :deep(.n-image) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.bill-details-section {
  margin-top: 20px;
}

.bill-details-section h3 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #e0e0e0;
}
</style>
