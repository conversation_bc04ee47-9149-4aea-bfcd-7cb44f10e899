using System;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;

namespace ParkingBoothApi.Helper
{
    /// <summary>
    /// Windows 動態庫 (V1.4) 的 C# 輔助類。
    /// 支援 USB、網路 (TCP9100)、序列埠、LPT。
    /// </summary>
    public sealed class PrinterHelper : IDisposable
    {
        private const string DllName = "JsPrinterDll_64.dll";        // ← 請改成實際 DLL 檔名
        private const int DefaultTimeout = 5_000;           // 網路連線逾時 (ms)

        private int _handle;                                // 由 DLL 傳回的連線句柄
        private bool _disposed;

        #region ───── P/Invoke 宣告 ────────────────────────────────────────────
        [DllImport(DllName, CallingConvention = CallingConvention.StdCall)]
        private static extern bool uniInitNetSev();

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall)]
        private static extern bool uniCloseNetServ();

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        private static extern int uniConnectNetPortByIp(string ip, int port = 9100, int Timeout = DefaultTimeout);

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall)]
        private static extern int uniOpenUsb();                                 // 自動尋找 USB

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall)]
        private static extern int uniOpenUsbByVidPid(int vid, int pid);         // 指定 VID/PID

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        private static extern int uniOpenCom(string lpCom, int BaudRate);       // COM1,9600…

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        private static extern int uniOpenLpt(string lpLptName);                 // LPT1…

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall)]
        private static extern bool uniClose(int fs);

        [DllImport(DllName, CallingConvention = CallingConvention.StdCall)]
        private static extern int uniWrite(int fs, byte[] sendBuf, int sendBufSize);

        // 功能指令‧列印 BMP（1B 2A 指令）
        [DllImport(DllName, CallingConvention = CallingConvention.StdCall, CharSet = CharSet.Ansi)]
        private static extern int uniPrintImg1b2a(int fs, string imgPath);

        // 功能指令‧彈錢箱 ESC p m t1 t2
        [DllImport(DllName, CallingConvention = CallingConvention.StdCall)]
        private static extern int unicreatCashboxContorlPulse(int fs, int m, int t1, int t2);
        #endregion

        #region ───── 建構 / 關閉 ──────────────────────────────────────────────
        private PrinterHelper(int handle)
        {
            _handle = handle;
        }

        /// <summary>用 USB (自動搜尋) 連線印表機。</summary>
        public static PrinterHelper FromUsb()
        {
            int h = uniOpenUsb();
            EnsureValidHandle(h, "USB");
            return new PrinterHelper(h);
        }

        /// <summary>用 VID/PID 連線 USB 印表機。</summary>
        public static PrinterHelper FromUsb(int vid, int pid)
        {
            int h = uniOpenUsbByVidPid(vid, pid);
            EnsureValidHandle(h, $"USB VID={vid:X4} PID={pid:X4}");
            return new PrinterHelper(h);
        }

        /// <summary>用 TCP/IP 連線印表機。</summary>
        public static PrinterHelper FromNetwork(string ip, int port = 9100, int timeoutMs = DefaultTimeout)
        {
            if (!uniInitNetSev()) throw new IOException("InitNetSev 失敗。");
            int h = uniConnectNetPortByIp(ip, port, timeoutMs);
            EnsureValidHandle(h, $"{ip}:{port}");
            return new PrinterHelper(h);
        }

        /// <summary>用序列埠連線印表機。</summary>
        public static PrinterHelper FromCom(string comPort, int baudRate = 9600)
        {
            int h = uniOpenCom(comPort, baudRate);
            EnsureValidHandle(h, $"COM {comPort}@{baudRate}");
            return new PrinterHelper(h);
        }

        /// <summary>用 LPT 連線印表機。</summary>
        public static PrinterHelper FromLpt(string lpt = "LPT1")
        {
            int h = uniOpenLpt(lpt);
            EnsureValidHandle(h, lpt);
            return new PrinterHelper(h);
        }

        private static void EnsureValidHandle(int handle, string desc)
        {
            if (handle <= 0) throw new IOException($"開啟印表機({desc})失敗，DLL 回傳 {handle}。");
        }

        /// <inheritdoc/>
        public void Dispose()
        {
            if (_disposed) return;

            if (_handle > 0) uniClose(_handle);
            _handle = 0;

            // 只要程式曾呼叫過 uniInitNetSev，就在離開時反初始化
            try { uniCloseNetServ(); } catch { /* 忽略 */ }

            _disposed = true;
            GC.SuppressFinalize(this);
        }
        #endregion

        #region ───── 主要功能 ────────────────────────────────────────────────
        /// <summary>列印純文字（自動換行，使用系統預設 ANSI 編碼）。</summary>
        public void PrintText(string text)
        {
            if (text is null) return;
            var encoding = Encoding.Default;               // ※ DLL 規定 ANSI
            var data = encoding.GetBytes(text);
            WriteRaw(data);
        }

        /// <summary>列印黑白 BMP（建議 1-bit BMP）。由 DLL 內建 1B 2A 指令處理。</summary>
        public void PrintBitmap(string bmpFilePath, bool throwIfNotExist = true)
        {
            if (!File.Exists(bmpFilePath))
            {
                if (throwIfNotExist) throw new FileNotFoundException("Bmp 檔不存在。", bmpFilePath);
                return;
            }

            int r = uniPrintImg1b2a(_handle, bmpFilePath);
            if (r <= 0) throw new IOException($"uniPrintImg1b2a 失敗，回傳 {r}。");
        }

        /// <summary>
        /// 彈出錢箱（ESC p m t1 t2）。
        /// m=0 使用腳位 2；t1/t2 為高低脈衝時間，單位 2 ms。
        /// </summary>
        public void OpenCashDrawer(byte m = 0, byte t1 = 100, byte t2 = 100)
        {
            int r = unicreatCashboxContorlPulse(_handle, m, t1, t2);
            if (r <= 0) throw new IOException($"開錢箱失敗，回傳 {r}。");
        }

        /// <summary>直接傳送 ESC/POS 原始指令。</summary>
        public void WriteRaw(byte[] bytes)
        {
            if (bytes is null || bytes.Length == 0) return;
            int written = uniWrite(_handle, bytes, bytes.Length);
            if (written <= 0) throw new IOException($"uniWrite 失敗，寫入 {written} 位元組。");
        }
        #endregion


        #region demo

                // 1. 透過 USB 自動搜尋連線
        using var printer = PrinterHelper.FromUsb();

        // 2. 列印文字
        printer.PrintText("您好，歡迎光臨！\n--------------------------------\n");

        // 3. 列印票據 LOGO（1‑bit BMP）
        printer.PrintBitmap(@"D:\logo.bmp");

        // 4. 換行並關單
        printer.PrintText("\n謝謝惠顧，請再度光臨！\n\n");

        // 5. 彈錢箱
        printer.OpenCashDrawer(m: 0, t1: 100, t2: 100);

        #endregion
    }
}
