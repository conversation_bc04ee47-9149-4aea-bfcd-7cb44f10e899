using System;
using System.IO;
using System.Linq;
using System.Collections.Generic;
using System.Runtime.InteropServices;
using System.Drawing;
using System.Drawing.Printing;
using System.Text;

namespace ParkingBoothApi.Helper
{
    /// <summary>
    /// A helper class for sending RAW data to printers, opening cash drawers via ESC/POS,
    /// and for GDI-based printing with font styling.
    /// </summary>
    public static class RawPrinterHelper
    {
        #region P/Invoke for RAW printing
        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Unicode)]
        private class DOCINFO
        {
            [MarshalAs(UnmanagedType.LPWStr)] public string pDocName;
            [MarshalAs(UnmanagedType.LPWStr)] public string pOutputFile;
            [MarshalAs(UnmanagedType.LPWStr)] public string pDataType;
        }

        [DllImport("winspool.drv", EntryPoint = "OpenPrinterW", SetLastError = true, CharSet = CharSet.Unicode)]
        private static extern bool OpenPrinter(string pPrinterName, out IntPtr phPrinter, IntPtr pDefault);

        [DllImport("winspool.drv", SetLastError = true)]
        private static extern bool StartDocPrinter(IntPtr hPrinter, int level, [In] DOCINFO pDocInfo);

        [DllImport("winspool.drv", SetLastError = true)]
        private static extern bool StartPagePrinter(IntPtr hPrinter);

        [DllImport("winspool.drv", SetLastError = true)]
        private static extern bool WritePrinter(IntPtr hPrinter, IntPtr pBytes, int dwCount, out int dwWritten);

        [DllImport("winspool.drv", SetLastError = true)]
        private static extern bool EndPagePrinter(IntPtr hPrinter);

        [DllImport("winspool.drv", SetLastError = true)]
        private static extern bool EndDocPrinter(IntPtr hPrinter);

        [DllImport("winspool.drv", SetLastError = true)]
        private static extern bool ClosePrinter(IntPtr hPrinter);
        #endregion

        #region RAW printing methods
        /// <summary>
        /// Sends raw bytes to the specified printer.
        /// </summary>
        public static bool SendBytesToPrinter(string printerName, IntPtr pBytes, int byteCount)
        {
            if (!OpenPrinter(printerName, out var hPrinter, IntPtr.Zero))
                return false;

            var docInfo = new DOCINFO { pDocName = "Raw Document", pDataType = "RAW" };
            if (!StartDocPrinter(hPrinter, 1, docInfo))
            {
                ClosePrinter(hPrinter);
                return false;
            }

            StartPagePrinter(hPrinter);
            WritePrinter(hPrinter, pBytes, byteCount, out _);
            EndPagePrinter(hPrinter);
            EndDocPrinter(hPrinter);
            ClosePrinter(hPrinter);
            return true;
        }

        /// <summary>
        /// Sends a byte array to the specified printer.
        /// </summary>
        public static bool SendBytesToPrinter(string printerName, byte[] bytes)
        {
            var unmanagedPointer = Marshal.AllocCoTaskMem(bytes.Length);
            Marshal.Copy(bytes, 0, unmanagedPointer, bytes.Length);
            bool result = SendBytesToPrinter(printerName, unmanagedPointer, bytes.Length);
            Marshal.FreeCoTaskMem(unmanagedPointer);
            return result;
        }

        /// <summary>
        /// Reads a file and sends its contents to the specified printer.
        /// </summary>
        public static bool SendFileToPrinter(string printerName, string filePath)
        {
            byte[] fileContents = File.ReadAllBytes(filePath);
            return SendBytesToPrinter(printerName, fileContents);
        }

        /// <summary>
        /// Sends a string (as ANSI by default) to the specified printer.
        /// </summary>
        public static bool SendStringToPrinter(string printerName, string content, Encoding encoding = null)
        {
            encoding ??= Encoding.Default;
            byte[] buffer = encoding.GetBytes(content);
            return SendBytesToPrinter(printerName, buffer);
        }

        /// <summary>
        /// Opens the cash drawer using the ESC/POS command on the specified printer.
        /// </summary>
        /// <param name="printerName">Printer queue name (e.g., "Generic / Text Only").</param>
        /// <param name="openCashDrawerCommand">ESC/POS command to open the drawer (default: "\x1B\x70\x00\x32\x32").</param>
        /// <param name="encoding">Text encoding for the command bytes (default: ANSI).</param>
        public static bool OpenCashDrawer(string printerName, string openCashDrawerCommand = "\x1B\x70\x00\x32\x32", Encoding encoding = null)
        {
            return SendStringToPrinter(printerName, openCashDrawerCommand, encoding);
        }
        #endregion

        #region GDI-based printing with font styling
        /// <summary>
        /// Prints a block of text using GDI, allowing font customization.
        /// </summary>
        /// <param name="printerName">Printer queue name.</param>
        /// <param name="text">Text content to print.</param>
        /// <param name="fontName">Font family name (e.g., "Arial").</param>
        /// <param name="fontSize">Font size in points.</param>
        /// <param name="fontStyle">Font style flags (Regular, Bold, Italic, etc.).</param>
        /// <param name="margins">Page margins in hundredths of an inch.</param>
        public static void PrintText(string printerName,
                                     string text,
                                     string fontName = "Arial",
                                     float fontSize = 12f,
                                     FontStyle fontStyle = FontStyle.Regular,
                                     Margins margins = null)
        {
            margins ??= new Margins(50, 50, 50, 50);
            using var pd = new PrintDocument
            {
                PrinterSettings = { PrinterName = printerName },
                DefaultPageSettings = { Margins = margins }
            };

            pd.PrintPage += (sender, e) =>
            {
                using var font = new Font(fontName, fontSize, fontStyle);
                e.Graphics.DrawString(text, font, Brushes.Black, e.MarginBounds);
            };

            pd.Print();
        }

        /// <summary>
        /// Prints multiple lines of text with pagination support and font customization.
        /// </summary>
        public static void PrintLines(string printerName,
                                      IEnumerable<string> lines,
                                      string fontName = "Arial",
                                      float fontSize = 12f,
                                      FontStyle fontStyle = FontStyle.Regular,
                                      Margins margins = null)
        {
            margins ??= new Margins(50, 50, 50, 50);
            using var pd = new PrintDocument
            {
                PrinterSettings = { PrinterName = printerName },
                DefaultPageSettings = { Margins = margins }
            };

            int currentLine = 0;
            pd.PrintPage += (sender, e) =>
            {
                using var font = new Font(fontName, fontSize, fontStyle);
                float lineHeight = font.GetHeight(e.Graphics);
                float yPos = e.MarginBounds.Top;
                var list = lines.ToList();

                while (currentLine < list.Count)
                {
                    string line = list[currentLine];
                    e.Graphics.DrawString(line, font, Brushes.Black, e.MarginBounds.Left, yPos);
                    yPos += lineHeight;
                    currentLine++;

                    if (yPos + lineHeight > e.MarginBounds.Bottom)
                    {
                        e.HasMorePages = true;
                        return;
                    }
                }

                e.HasMorePages = false;
            };

            pd.Print();
        }
        #endregion

        #region Usage Examples
        /*
         * 1) Send simple plain text via RAW:
         *    string printer = "Generic / Text Only";
         *    RawPrinterHelper.SendStringToPrinter(printer, "Hello, world!\n");
         *
         * 2) Open cash drawer:
         *    string drawerPrinter = "Generic / Text Only";
         *    RawPrinterHelper.OpenCashDrawer(drawerPrinter);
         *
         * 3) Print styled text with GDI:
         *    RawPrinterHelper.PrintText(
         *        printerName: "Your Printer Name",
         *        text: "重要報表\nReport Date: 2025-08-04",
         *        fontName: "Microsoft JhengHei",
         *        fontSize: 14f,
         *        fontStyle: FontStyle.Bold,
         *        margins: new Margins(100, 100, 50, 50)
         *    );
         *
         * 4) Print multiple lines with automatic pagination:
         *    var lines = new List<string> { "第一行", "第二行", "第三行" };
         *    RawPrinterHelper.PrintLines(
         *        printerName: "Your Printer Name",
         *        lines: lines,
         *        fontName: "Courier New",
         *        fontSize: 10f,
         *        fontStyle: FontStyle.Italic
         *    );
         */
        #endregion
    }
}
