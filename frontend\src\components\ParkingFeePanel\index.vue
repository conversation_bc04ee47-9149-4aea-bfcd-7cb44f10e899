<template>
  <div class="parking-fee-panel">
    <!-- 标题 -->
    <div class="panel-title">
      <h3>停车费用信息</h3>
    </div>

    <!-- 车牌号区域 -->
    <div class="plate-number-section">
      <div class="plate-display">
        <span class="plate-prefix">空</span>
        <NInput
          v-model:value="editablePlateNumber"
          class="plate-input"
          placeholder="请输入车牌号"
          :bordered="false"
          :show-count="false"
        />
      </div>
      <NButton type="success" class="modify-btn" :disabled="!selectedMonitorData">
        确定修改
      </NButton>
    </div>

    <!-- 车辆信息列表 -->
    <div class="vehicle-info-section">
      <div class="info-list">
        <div class="info-item">
          <span class="info-label">卡号</span>
          <span class="info-value">{{ selectedMonitorData ? vehicleInfo.cardNumber : '' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">车辆类型</span>
          <NSelect
            v-model:value="selectedVehicleType"
            :options="vehicleTypeOptions"
            size="small"
            class="vehicle-type-select"
            :disabled="!selectedMonitorData"
          />
        </div>
        <div class="info-item">
          <span class="info-label">通道类型</span>
          <span class="info-value">{{ selectedMonitorData ? vehicleInfo.tempType : '' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">入场时间</span>
          <span class="info-value">{{ selectedMonitorData ? vehicleInfo.entryTime : '' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">入场地点</span>
          <span class="info-value">{{ selectedMonitorData ? vehicleInfo.entryLocation : '' }}</span>
        </div>
        <div class="info-item">
          <span class="info-label">停车时长</span>
          <span class="info-value">{{ selectedMonitorData ? vehicleInfo.duration : '' }}</span>
        </div>
      </div>
    </div>

    <!-- 费用显示 -->
    <div class="fee-display-section">
      <div class="fee-amount-large">
        <span class="fee-label-large">请收费</span>
        <span class="fee-value-large">{{ selectedMonitorData ? '0.10MOP' : '' }}</span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-buttons-section">
      <div class="button-grid">
        <NButton type="primary" class="action-btn" disabled>
          抵付券
        </NButton>
        <NButton type="primary" class="action-btn" :disabled="!selectedMonitorData" @click="handleFreePass">
          免费
        </NButton>
        <NButton type="primary" class="action-btn" :disabled="!selectedMonitorData" @click="handleCashPayment">
          现金收费
        </NButton>
        <NButton type="primary" class="action-btn" disabled>
          澳门通支付
        </NButton>
      </div>
      <NButton type="primary" class="full-width-btn" disabled>
        澳门通充值
      </NButton>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { NInput, NButton, NSelect } from 'naive-ui'
import type { MonitorData } from '../../data/mockMonitorData'

defineOptions({
  name: 'ParkingFeePanel'
})

// Props
interface Props {
  selectedMonitorData?: MonitorData | null
}

const props = withDefaults(defineProps<Props>(), {
  selectedMonitorData: null
})

// Emits
interface Emits {
  (e: 'cash-payment-success'): void
  (e: 'free-pass-click'): void
}

const emit = defineEmits<Emits>()

// 车辆类型选择
const selectedVehicleType = ref('电单车')
const vehicleTypeOptions = [
  { label: '电单车', value: '电单车' },
  { label: '私家车', value: '私家车' },
  { label: '货车', value: '货车' },
  { label: '其他', value: '其他' }
]

// 可编辑车牌号
const editablePlateNumber = ref('')

// 车辆信息
const vehicleInfo = ref({
  plateNumber: '',
  vehicleType: '电单车',
  color: '蓝色',
  entryTime: '',
  entryLocation: '电单车入口',
  duration: '5分钟14秒',
  fee: '0.10MOP',
  cardNumber: '4a17c35c',
  tempType: '临时车'
})

// 现金收费处理方法
const handleCashPayment = () => {
  if (props.selectedMonitorData) {
    // 触发现金收费成功事件
    emit('cash-payment-success')
  }
}

// 免费按钮点击处理方法
const handleFreePass = () => {
  if (props.selectedMonitorData) {
    // 触发免费按钮点击事件
    emit('free-pass-click')
  }
}





// 监听选中的监控数据变化
watch(() => props.selectedMonitorData, (newData) => {
  if (newData) {
    const plateNumber = newData.plateNumber || newData.id
    const vehicleType = '电单车'

    vehicleInfo.value = {
      plateNumber: plateNumber,
      vehicleType: vehicleType,
      color: '蓝色',
      entryTime: newData.timestamp,
      entryLocation: '电单车入口',
      duration: '5分钟14秒',
      fee: '0.10MOP',
      cardNumber: '4a17c35c',
      tempType: '临时车'
    }
    editablePlateNumber.value = plateNumber
    // 同步更新车辆类型下拉框的值
    selectedVehicleType.value = vehicleType
  } else {
    // 清空数据
    editablePlateNumber.value = ''
    selectedVehicleType.value = ''
  }
}, { immediate: true })
</script>

<style scoped>
.parking-fee-panel {
  width: 420px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 12px;
  padding: 20px;
  color: #333;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 标题 */
.panel-title {
  text-align: center;
  margin-bottom: 10px;
}

.panel-title h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 车牌号区域 */
.plate-number-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
  gap: 20px;
}

.plate-display {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.plate-prefix {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  background: transparent;
  padding: 8px 12px;
  border-radius: 4px;
  border: 1px solid #ddd;
  min-width: 50px;
  text-align: center;
}

.plate-input {
  flex: 1;
  max-width: 200px;
}

.plate-input .n-input-wrapper {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.plate-input .n-input__input-el {
  font-size: 28px !important;
  font-weight: bold !important;
  color: #333 !important;
  background: transparent !important;
  border: none !important;
  padding: 8px 12px !important;
  text-align: left !important;
}

.plate-input .n-input__input-el::placeholder {
  color: #999 !important;
  font-weight: normal !important;
}

.modify-btn {
  font-size: 14px;
  padding: 8px 20px;
  height: 36px;
  min-width: 80px;
  flex-shrink: 0;
}

/* 车辆基本信息 */
.vehicle-info-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 15px;
}

.info-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #666;
  min-width: 80px;
}

.info-value {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.vehicle-type-select {
  width: 120px;
}

/* 费用显示区域 */
.fee-display-section {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  margin: 20px 0;
}

.fee-amount-large {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.fee-label-large {
  color: #333;
  font-size: 18px;
  font-weight: 500;
}

.fee-value-large {
  color: #ff4444;
  font-size: 36px;
  font-weight: bold;
}

/* 操作按钮区域 */
.action-buttons-section {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.button-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
}

.action-btn {
  height: 45px;
  font-size: 14px;
  font-weight: 500;
  background: #4a90e2;
  border: none;
}

.action-btn:hover {
  background: #357abd;
}

.full-width-btn {
  height: 45px;
  font-size: 14px;
  font-weight: 500;
  background: #4a90e2;
  border: none;
  width: 100%;
}

.full-width-btn:hover {
  background: #357abd;
}
</style>
