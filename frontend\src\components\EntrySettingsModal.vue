<template>
  <BaseModal
    :visible="visible"
    title="进出口设置"
    size="extra-large"
    @update:visible="$emit('update:visible', $event)"
    @close="$emit('close')"
  >
    <!-- 标签页和搜索区域 -->
    <div class="settings-controls">
      <div class="tab-buttons">
        <button
          v-for="tab in tabs"
          :key="tab"
          @click="activeTab = tab"
          class="tab-btn"
          :class="{ active: activeTab === tab }"
        >
          {{ tab }}
        </button>
      </div>

      <div class="search-and-actions">
        <div class="search-container">
          <input
            v-model="searchKeyword"
            @input="debouncedSearch"
            type="text"
            placeholder="通道名称搜索"
            class="search-input"
          />
        </div>

        <div class="action-buttons">
          <button class="action-btn search">搜索</button>
          <button class="action-btn long-view">一键长视</button>
          <button class="action-btn short-view">一键短视</button>
        </div>
      </div>
    </div>

    <!-- 通道列表 -->
    <div class="channels-section">
      <div class="table-header">
        <div class="table-cell">通道名称</div>
        <div class="table-cell">状态</div>
        <div class="table-cell">挡车器控制</div>
        <div class="table-cell">地感控制</div>
      </div>

      <div class="table-body">
        <div
          v-for="channel in filteredChannels"
          :key="channel.id"
          class="table-row"
        >
          <div class="table-cell channel-name">{{ channel.name }}</div>
          <div class="table-cell channel-status">
            <span class="status-badge active">{{ channel.status }}</span>
          </div>
          <div class="table-cell modal-control">
            <div class="control-buttons">
              <button
                class="control-btn"
                :class="{ active: channel.modalControl === '禁用' }"
                @click="toggleModalControl(channel, '禁用')"
              >
                禁用
              </button>
              <button
                class="control-btn"
                :class="{ active: channel.modalControl === '常规' }"
                @click="toggleModalControl(channel, '常规')"
              >
                常规
              </button>
            </div>
          </div>
          <div class="table-cell distance-control">
            <div class="control-buttons">
              <button
                class="control-btn"
                :class="{ active: channel.distanceControl === '禁用' }"
                @click="toggleDistanceControl(channel, '禁用')"
              >
                禁用
              </button>
              <button
                class="control-btn"
                :class="{ active: channel.distanceControl === '常规' }"
                @click="toggleDistanceControl(channel, '常规')"
              >
                常规
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <template #footer>
      <button class="modal-btn cancel" @click="$emit('close')">取消</button>
      <button class="modal-btn confirm" @click="saveSettings">保存设置</button>
    </template>
  </BaseModal>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import BaseModal from './BaseModal.vue'
import { debounce } from '../utils/helpers'

interface Channel {
  id: number
  name: string
  status: string
  modalControl: string
  distanceControl: string
}

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
  (e: 'save', channels: Channel[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const activeTab = ref('全部')
const searchKeyword = ref('')

const tabs = ['全部', '入口地点', '出口地点']

// 通道数据
const channels = ref<Channel[]>([
  { id: 1, name: '私家车入口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 2, name: '私家车出口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 3, name: '电单车入口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 4, name: '电单车出口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 5, name: 'B2入口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 6, name: 'B2出口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 7, name: 'B2电单车入口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 8, name: 'B2电单车出口', status: '已停到位', modalControl: '常规', distanceControl: '禁用' },
  { id: 9, name: '私家车入口2', status: '已停到位', modalControl: '常规', distanceControl: '禁用' }
])

// 过滤后的通道列表
const filteredChannels = computed(() => {
  let filtered = channels.value

  // 根据标签页过滤
  if (activeTab.value === '入口地点') {
    filtered = filtered.filter(channel => channel.name.includes('入口'))
  } else if (activeTab.value === '出口地点') {
    filtered = filtered.filter(channel => channel.name.includes('出口'))
  }

  // 根据搜索关键词过滤
  if (searchKeyword.value) {
    filtered = filtered.filter(channel =>
      channel.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  return filtered
})

// 防抖搜索
const debouncedSearch = debounce(() => {
  // 搜索逻辑已在计算属性中处理
}, 300)

// 切换挡车器控制
const toggleModalControl = (channel: Channel, control: string) => {
  channel.modalControl = control
}

// 切换地感控制
const toggleDistanceControl = (channel: Channel, control: string) => {
  channel.distanceControl = control
}

// 保存设置
const saveSettings = () => {
  emit('save', channels.value)
  emit('close')
}
</script>

<style scoped>
.settings-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;
}

.tab-buttons {
  display: flex;
  gap: 8px;
}

.tab-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;
}

.tab-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.tab-btn:hover:not(.active) {
  background: #f8f9fa;
}

.search-and-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-input {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 200px;
  outline: none;
}

.search-input:focus {
  border-color: #007bff;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 12px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.action-btn:hover {
  background: #f8f9fa;
}

.channels-section {
  border: 1px solid #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.table-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1.5fr;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.table-body {
  max-height: 400px;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr 1.5fr;
  border-bottom: 1px solid #f0f0f0;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-cell {
  padding: 12px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.channel-name {
  font-weight: 500;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #d4edda;
  color: #155724;
}

.control-buttons {
  display: flex;
  gap: 4px;
}

.control-btn {
  padding: 4px 8px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.control-btn.active {
  background: #007bff;
  color: white;
  border-color: #007bff;
}

.control-btn:hover:not(.active) {
  background: #f8f9fa;
}

.modal-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.modal-btn.cancel {
  background: #6c757d;
  color: white;
}

.modal-btn.cancel:hover {
  background: #5a6268;
}

.modal-btn.confirm {
  background: #007bff;
  color: white;
}

.modal-btn.confirm:hover {
  background: #0056b3;
}
</style>
