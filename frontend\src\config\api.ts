// API配置
export const API_CONFIG = {
  // 基础API地址
  BASE_URL: 'http://10.0.0.149:9001/yard',

  // 超时时间
  TIMEOUT: 60000,

  // 开发环境配置
  DEV: {
    BASE_URL: 'http://10.0.0.149:9001/yard',
    TIMEOUT: 60000
  },

  // 生产环境配置
  PROD: {
    BASE_URL: 'http://10.0.0.149:9001/yard',
    TIMEOUT: 30000
  }
}

// 根据环境获取API配置
export const getApiConfig = () => {
  if (process.env.NODE_ENV === 'development') {
    return API_CONFIG.DEV
  }
  return API_CONFIG.PROD
}

// API端点定义
export const API_ENDPOINTS = {
  // 认证相关
  AUTH: {
    LOGIN: '/auth/login',
    LOGOUT: '/auth/logout',
    OAUTH_TOKEN: '/oauth/token',
    USER_INFO: '/user/findUserByName',
    CHANGE_PASSWORD: '/user/updatePwd',
    RESET_PASSWORD: '/user/resetUserPwd'
  },

  //基础接口
  BASE: {
    CAMERA_SHOOT: "/callCenter/openOrReshoot?channnelMac=", //	0 開閘  3關閘  1 主相機補拍  offOrOn 打開或關閉 ws
  },
  
  // 首页统计
  HOME: {
    SUM_INFO: '/home/<USER>/sumInfoReport',
    STATISTICS: '/home/<USER>/statisticsReport',
    CAR_NUM_REPORT: '/home/<USER>/CarNumReport',
    CHANNEL_STATUS: '/home/<USER>/channelStatusReport'
  },
  
  // 车辆管理
  VEHICLE: {
    RECORDS: '/report/getCarNumInfo',
    IN_PARK: '/report/getCarInInfo',
    CHARGE_DETAILS: '/report/getChargeInfo',
    UPDATE_PLATE: '/callCenter/updateCaNo',
    FREE_PASS: '/callCenter/confirm',
    SIMILAR_PLATES: '/callCenter/findsimilarCar',
    RECHARGE: '/callCenter/chargeAgain'
  },
  
  // 设备控制
  DEVICE: {
    CONTROL_GATE: '/callCenter/openOrReshoot',
    CHANNELS: '/channel/findAllInfo',
    PARKING_GROUPS: '/group/findAll'
  },
  
  // 系统管理
  SYSTEM: {
    PARAMS: '/paramster/getCarParams',
    SAVE_PARAMS: '/paramster/batchUpdate',
    SHIFT_RECORDS: '/report/getShiftRecord'
  },
  
  // 文件相关
  FILE: {
    VEHICLE_PHOTO: '/getPicture/find',
    EXPORT_RECORDS: '/report/getAllCarNumInfo',
    EXPORT_CHARGES: '/report/getAllChargeInfo'
  }
}

// 请求类型定义
export const REQUEST_TYPES = {
  JSON: 'json',
  FORM: 'form',
  QUERY: 'query',
  UPLOAD: 'upload',
  DOWNLOAD: 'download'
}

// 响应状态码
export const RESPONSE_STATUS = {
  SUCCESS: 1,
  UNAUTHORIZED: 401,
  TOKEN_EXPIRED: 2,
  PERMISSION_DENIED: 3,
  ERROR: 0
}

export default API_CONFIG
