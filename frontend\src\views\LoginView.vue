<template>
  <div class="login-container">
    <div class="login-header">
      <div class="logo">
        <h1>HONGRUI</h1>
      </div>
      <div class="system-title">
        泓睿 智慧停車場收費管理系統
      </div>
    </div>
    
    <div class="login-content">
      <div class="welcome-text">
        歡迎你使用，服務中心！
      </div>
      
      <div class="login-form-container">
        <form @submit.prevent="handleLogin" class="login-form">
          <div class="language-selector">
            <select v-model="selectedLanguage" class="language-select">
              <option value="zh-TW">繁體中文</option>
              <option value="zh-CN">简体中文</option>
              <option value="en">English</option>
            </select>
          </div>
          
          <div class="form-group">
            <div class="input-group">
              <span class="input-icon">👤</span>
              <input 
                v-model="loginForm.username"
                type="text" 
                placeholder="用戶名"
                class="form-input"
                required
              />
            </div>
          </div>
          
          <div class="form-group">
            <div class="input-group">
              <span class="input-icon">🔒</span>
              <input 
                v-model="loginForm.password"
                type="password" 
                placeholder="密碼"
                class="form-input"
                required
              />
            </div>
          </div>
          
          <button 
            type="submit" 
            class="login-button"
            :disabled="isLoading"
          >
            {{ isLoading ? '登錄中...' : '登錄' }}
          </button>
        </form>
      </div>
    </div>
    
    <div class="login-footer">
      <p>© 泓睿停车场系统</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const selectedLanguage = ref('zh-TW')
const isLoading = ref(false)

const loginForm = ref({
  username: '',
  password: ''
})

const handleLogin = async () => {
  if (!loginForm.value.username || !loginForm.value.password) {
    alert('請輸入用戶名和密碼')
    return
  }
  
  isLoading.value = true
  
  try {
    const success = await authStore.login(loginForm.value.username, loginForm.value.password)
    console.log('success',success);
    if (success) {
      router.push('/')
    } else {
      alert('登錄失敗，請檢查用戶名和密碼')
    }
  } catch (error) {
    console.error('Login error:', error)
    alert('登錄過程中發生錯誤，請稍後重試')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
/* 确保登录页面完全覆盖整个视窗 */
* {
  box-sizing: border-box;
}
.login-container {
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%);
  display: flex;
  flex-direction: column;
  font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
  overflow: hidden;
  z-index: 9999;
}

.login-header {
  padding: 20px 40px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
}

.logo h1 {
  margin: 0;
  font-size: 2em;
  font-weight: bold;
  letter-spacing: 2px;
}

.system-title {
  font-size: 1.1em;
  opacity: 0.9;
}

.login-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  width: 100%;
  box-sizing: border-box;
  min-height: 0;
}

.welcome-text {
  color: white;
  font-size: 2.5em;
  font-weight: 300;
  margin-bottom: 60px;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
  white-space: nowrap;
}

.login-form-container {
  background: white;
  border-radius: 12px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 100%;
  max-width: 450px;
  margin: 0 auto;
  box-sizing: border-box;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.language-selector {
  text-align: right;
  margin-bottom: 10px;
}

.language-select {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #666;
}

.form-group {
  margin-bottom: 20px;
}

.input-group {
  position: relative;
  display: flex;
  align-items: center;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  background: white;
  transition: border-color 0.3s ease;
}

.input-group:focus-within {
  border-color: #4A90E2;
}

.input-icon {
  padding: 0 15px;
  font-size: 18px;
  color: #999;
}

.form-input {
  flex: 1;
  padding: 15px 15px 15px 0;
  border: none;
  outline: none;
  font-size: 16px;
  background: transparent;
}

.form-input::placeholder {
  color: #999;
}

.login-button {
  background: linear-gradient(135deg, #4A90E2, #357ABD);
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.login-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #357ABD, #2968A3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(74, 144, 226, 0.4);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.login-footer {
  text-align: center;
  padding: 20px;
  color: white;
  opacity: 0.8;
  font-size: 14px;
  flex-shrink: 0;
  width: 100%;
  box-sizing: border-box;
}

@media (max-width: 768px) {
  .login-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  .login-header {
    flex-direction: column;
    gap: 10px;
    text-align: center;
    padding: 15px 20px;
  }

  .welcome-text {
    font-size: 2em;
    margin-bottom: 30px;
  }

  .login-content {
    padding: 20px 15px;
  }

  .login-form-container {
    max-width: none;
    margin: 0;
    padding: 30px 20px;
    border-radius: 8px;
  }

  .form-input {
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .login-footer {
    padding: 15px 20px;
  }
}

@media (max-width: 480px) {
  .login-header {
    padding: 10px 15px;
  }

  .welcome-text {
    font-size: 1.5em;
    margin-bottom: 20px;
  }

  .login-content {
    padding: 15px 10px;
  }

  .login-form-container {
    padding: 20px 15px;
    border-radius: 6px;
  }

  .logo h1 {
    font-size: 1.5em;
  }

  .system-title {
    font-size: 0.8em;
  }

  .login-footer {
    padding: 10px 15px;
    font-size: 12px;
  }
}

/* 超宽屏幕优化 */
@media (min-width: 1920px) {
  .login-form-container {
    max-width: 500px;
    padding: 50px;
  }

  .welcome-text {
    font-size: 3em;
    margin-bottom: 80px;
  }

  .logo h1 {
    font-size: 2.2em;
  }
}

/* 确保在所有设备上都能正确显示 */
@media (orientation: landscape) and (max-height: 600px) {
  .welcome-text {
    font-size: 1.8em;
    margin-bottom: 30px;
  }

  .login-content {
    padding: 20px;
  }

  .login-header {
    padding: 15px 40px;
  }

  .login-footer {
    padding: 15px 40px;
  }
}
</style>
