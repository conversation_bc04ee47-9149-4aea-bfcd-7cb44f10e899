<template>
  <div class="monitor-grid-container">
    <!-- 监控图片网格 -->
    <div class="monitor-grid">
      <div
        v-for="monitor in currentPageData"
        :key="monitor.id"
        class="monitor-card"
        :class="{ 'selected': selectedMonitor?.id === monitor.id }"
        @click="handleSelect(monitor)"
        @dblclick="handleDoubleClick(monitor)"
      >
        <div class="monitor-content">
          <img 
            :src="monitor.image" 
            :alt="monitor.plateNumber || '无车牌'" 
            class="monitor-image" 
            @error="handleImageError"
          />
          
          <!-- 车牌号覆盖层 -->
          <!-- <div v-if="monitor.plateNumber" class="plate-overlay">
            <span class="plate-number">{{ monitor.plateNumber }}</span>
          </div> -->
          
          <!-- 监控信息 -->
          <div class="monitor-info">
            <span class="monitor-id">{{ monitor.id }}</span>
            <span class="timestamp">{{ monitor.timestamp }}</span>
          </div>
          
        </div>
      </div>
    </div>
    
    <!-- 分页控制 -->
    <div class="pagination-controls">
      <div class="page-info">
        <NButton 
          @click="previousPage" 
          :disabled="currentPage === 1"
          size="small"
          text
        >
          &lt;
        </NButton>
        <span class="page-numbers">
          <span 
            v-for="page in totalPages" 
            :key="page"
            :class="{ 'active': page === currentPage }"
            @click="goToPage(page)"
            class="page-number"
          >
            {{ page }}
          </span>
        </span>
        <NButton 
          @click="nextPage" 
          :disabled="currentPage >= totalPages"
          size="small"
          text
        >
          &gt;
        </NButton>
      </div>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// 定义监控数据类型
interface MonitorData {
  id: string
  plateNumber: string
  timestamp: string
  status: string
  image: string
  selected?: boolean
}

// Props
interface Props {
  data: MonitorData[]
  pageSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  pageSize: 6
})

// Emits
const emit = defineEmits<{
  select: [monitor: MonitorData]
  doubleClick: [monitor: MonitorData]
}>()

// 暴露方法给父组件
const clearSelection = () => {
  selectedMonitor.value = null
}

defineExpose({
  clearSelection
})

// 响应式数据
const currentPage = ref(1)
const selectedMonitor = ref<MonitorData | null>(null)

// 计算属性
const totalPages = computed(() => {
  return Math.ceil(props.data.length / props.pageSize)
})

const currentPageData = computed(() => {
  const start = (currentPage.value - 1) * props.pageSize
  const end = start + props.pageSize
  return props.data.slice(start, end)
})

// 方法
const handleSelect = (monitor: MonitorData) => {
  selectedMonitor.value = monitor
  emit('select', monitor)
}

const handleDoubleClick = (monitor: MonitorData) => {
  emit('doubleClick', monitor)
}

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/api/placeholder/240/180'
}

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const goToPage = (page: number) => {
  currentPage.value = page
}

// 监听数据变化，重置到第一页
watch(() => props.data, () => {
  currentPage.value = 1
  selectedMonitor.value = null
})
</script>

<style scoped>
.monitor-grid-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.monitor-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  width: 100%;
  margin-bottom: 20px;
  align-content: flex-start;
}

.monitor-card {
  background: #2a2a2a;
  border: 2px solid #555;
  border-radius: 4px;
  position: relative;
  transition: all 0.3s ease;
  overflow: hidden;
  cursor: pointer;
  flex: 0 0 calc(33.333% - 10px);
  aspect-ratio: 16/9;
}

.monitor-card.selected {
  border: 2px solid #4a9eff;
  box-shadow: 0 0 10px rgba(74, 158, 255, 0.3);
}

.monitor-card:hover {
  border-color: #6ab7ff;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.monitor-content {
  position: relative;
  width: 100%;
  height: 100%;
}

.monitor-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: #e9ecef;
}

.plate-overlay {
  position: absolute;
  top: 40%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 2px;
  font-weight: bold;
  font-size: 12px;
  font-family: 'Courier New', monospace;
  border: 1px solid #666;
}

.monitor-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 6px;
  display: flex;
  justify-content: space-between;
  font-size: 10px;
  font-family: 'Courier New', monospace;
  height: 50px;
  align-items: center;
}

.monitor-id {
  font-weight: bold;
  color: #4a9eff;
  font-size: 20px;
}

.timestamp {
  opacity: 0.9;
  color: #ccc;
  font-size: 18px;
}

.monitor-status {
  position: absolute;
  top: 6px;
  right: 6px;
}

.status-badge {
  background: rgba(0, 0, 0, 0.8);
  color: #4a9eff;
  padding: 3px 6px;
  border-radius: 2px;
  font-size: 10px;
  font-weight: bold;
  border: 1px solid #4a9eff;
}

/* 分页控制 */
.pagination-controls {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
  padding: 20px 0;
  flex-shrink: 0;
  margin-top: auto;
  /* background: #1a1a1a; */
  /* border-top: 1px solid #333; */
}

.page-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s ease;
}

.page-number:hover {
  background: #f0f0f0;
  color: #333;
}

.page-number.active {
  background: #007bff;
  color: white;
}



/* 响应式设计 */
@media (max-width: 1200px) {
  .monitor-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .monitor-grid {
    grid-template-columns: 1fr;
  }
  
  .pagination-controls {
    position: static;
    justify-content: center;
    margin-top: 20px;
  }
}
</style>
