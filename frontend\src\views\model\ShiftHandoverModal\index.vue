<template>
  <NModal
    :show="visible"
    preset="card"
    title="交班确认"
    :mask-closable="false"
    :closable="true"
    style="width: 600px;"
    @update:show="handleUpdateVisible"
    @close="handleClose"
  >
    <!-- 用户信息 -->
    <div class="user-info">
      <span class="user-text">当前用户：{{ currentUser }}，请确认交班信息！</span>
      <span class="shift-time">上次交班时间：{{ lastShiftTime }}</span>
    </div>

    <!-- 出口岗亭当前班次数据汇总 -->
    <div class="data-section">
      <div class="section-header">出口岗亭当前班次数据汇总</div>
      <div class="data-grid">
        <div class="data-row">
          <div class="data-item">
            <span class="label">应收金额</span>
            <span class="value">{{ exitData.receivableAmount }}MOP</span>
          </div>
          <div class="data-item">
            <span class="label">现金金额</span>
            <span class="value">{{ exitData.cashAmount }}MOP</span>
          </div>
        </div>
        <div class="data-row">
          <div class="data-item">
            <span class="label">免费金额</span>
            <span class="value">{{ exitData.freeAmount }}MOP</span>
          </div>
          <div class="data-item">
            <span class="label">抵扣金额</span>
            <span class="value">{{ exitData.deductionAmount }}MOP</span>
          </div>
        </div>
        <div class="data-row">
          <div class="data-item">
            <span class="label">抵扣时长</span>
            <span class="value">{{ exitData.deductionTime }}</span>
          </div>
          <div class="data-item">
            <span class="label">抵扣张数</span>
            <span class="value">{{ exitData.deductionCount }}</span>
          </div>
        </div>
        <div class="data-row">
          <div class="data-item">
            <span class="label">实际抵扣</span>
            <span class="value">{{ exitData.actualDeduction }}MOP</span>
          </div>
          <div class="data-item">
            <span class="label">--</span>
            <span class="value">--</span>
          </div>
        </div>
        <div class="data-row">
          <div class="data-item">
            <span class="label">总放行</span>
            <span class="value">{{ exitData.totalRelease }}次</span>
          </div>
          <div class="data-item">
            <span class="label">正常放行</span>
            <span class="value">{{ exitData.normalRelease }}次</span>
          </div>
        </div>
        <div class="data-row">
          <div class="data-item">
            <span class="label">异常放行</span>
            <span class="value">{{ exitData.abnormalRelease }}次</span>
          </div>
          <div class="data-item">
            <span class="label">免费放行</span>
            <span class="value">{{ exitData.freeRelease }}次</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 中央岗亭当前班次数据 -->
    <div class="data-section">
      <div class="section-header">中央岗亭当前班次数据</div>
      <div class="data-grid">
        <div class="data-row">
          <div class="data-item">
            <span class="label">应收金额</span>
            <span class="value">{{ centralData.receivableAmount }}MOP</span>
          </div>
          <div class="data-item">
            <span class="label">现金金额</span>
            <span class="value">{{ centralData.cashAmount }}MOP</span>
          </div>
        </div>
        <div class="data-row">
          <div class="data-item">
            <span class="label">免费金额</span>
            <span class="value">{{ centralData.freeAmount }}MOP</span>
          </div>
          <div class="data-item">
            <span class="label">抵扣金额</span>
            <span class="value">{{ centralData.deductionAmount }}MOP</span>
          </div>
        </div>
        <div class="data-row">
          <div class="data-item">
            <span class="label">抵扣时长</span>
            <span class="value">{{ centralData.deductionTime }}</span>
          </div>
          <div class="data-item">
            <span class="label">抵扣张数</span>
            <span class="value">{{ centralData.deductionCount }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="modal-footer">
        <NButton @click="handleCancel" size="medium">
          取消交班
        </NButton>
        <NButton type="primary" @click="handleConfirm" :loading="confirming" size="medium">
          确认交班
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { NModal, NButton } from 'naive-ui'

defineOptions({
  name: 'ShiftHandoverModal'
})

// Props
interface Props {
  visible: boolean
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

// Emits
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', data: any): void
}

const emit = defineEmits<Emits>()

// 状态
const confirming = ref(false)

// 用户信息
const currentUser = ref('aofe')
const lastShiftTime = ref('2025-07-22 17:51:11')

// 出口岗亭数据
const exitData = reactive({
  receivableAmount: 173,
  cashAmount: 173,
  freeAmount: 0,
  deductionAmount: 0,
  deductionTime: 0,
  deductionCount: 0,
  actualDeduction: 0,
  totalRelease: 4,
  normalRelease: 2,
  abnormalRelease: 2,
  freeRelease: 0
})

// 中央岗亭数据
const centralData = reactive({
  receivableAmount: 0,
  cashAmount: 0,
  freeAmount: 0,
  deductionAmount: 0,
  deductionTime: 0,
  deductionCount: 0
})

// 方法
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('update:visible', false)
}

const handleConfirm = async () => {
  confirming.value = true
  try {
    // 模拟交班确认API调用
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    const shiftData = {
      currentUser: currentUser.value,
      lastShiftTime: lastShiftTime.value,
      exitData: { ...exitData },
      centralData: { ...centralData },
      confirmTime: new Date().toLocaleString('zh-CN')
    }
    
    emit('confirm', shiftData)
    emit('update:visible', false)
  } catch (error) {
    console.error('交班确认失败:', error)
  } finally {
    confirming.value = false
  }
}

// 监听器
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置状态
    confirming.value = false
    // 这里可以重新获取最新的班次数据
    console.log('获取最新班次数据')
  }
})
</script>

<style scoped>
.user-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 12px 16px;
  background: #f8f9fa;
  border-radius: 4px;
}

.user-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.shift-time {
  font-size: 13px;
  color: #666;
}

.data-section {
  margin-bottom: 20px;
}

.section-header {
  background: #e9ecef;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  text-align: center;
  margin-bottom: 1px;
}

.data-grid {
  border: 1px solid #dee2e6;
}

.data-row {
  display: flex;
  border-bottom: 1px solid #dee2e6;
}

.data-row:last-child {
  border-bottom: none;
}

.data-item {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  border-right: 1px solid #dee2e6;
}

.data-item:last-child {
  border-right: none;
}

.label {
  font-size: 13px;
  color: #666;
}

.value {
  font-size: 13px;
  color: #333;
  font-weight: 500;
}

.modal-footer {
  display: flex;
  justify-content: center;
  gap: 16px;
  padding-top: 16px;
}
</style>
