{"openapi": "3.0.1", "info": {"title": "Parking Booth API", "description": "API for Parking Booth Management System with hardware integration", "contact": {"name": "Parking Booth Support", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/Hardware/printer/status": {"get": {"tags": ["Hardware"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrinterStatusDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrinterStatusDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrinterStatusDto"}}}}}}}, "/api/Hardware/printer/print": {"post": {"tags": ["Hardware"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/PrintTicketDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrintTicketDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PrintTicketDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrinterResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrinterResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrinterResponseDto"}}}}}}}, "/api/Hardware/printer/test": {"post": {"tags": ["Hardware"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrinterResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrinterResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrinterResponseDto"}}}}}}}, "/api/Hardware/cashdrawer/open": {"post": {"tags": ["Hardware"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OpenCashDrawerDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/OpenCashDrawerDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/OpenCashDrawerDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CashDrawerResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CashDrawerResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CashDrawerResponseDto"}}}}}}}, "/api/Hardware/cashdrawer/test": {"post": {"tags": ["Hardware"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CashDrawerResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CashDrawerResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CashDrawerResponseDto"}}}}}}}, "/api/Hardware/printer/list": {"get": {"tags": ["Hardware"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/PrinterListResponseDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/PrinterListResponseDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PrinterListResponseDto"}}}}}}}, "/api/health": {"get": {"tags": ["ParkingBoothApi"], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StringDateTimeString<>f__AnonymousType1"}}}}}}}}, "components": {"schemas": {"CashDrawerResponseDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "error": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "printerUsed": {"type": "string", "nullable": true}}, "additionalProperties": false}, "OpenCashDrawerDto": {"type": "object", "properties": {"printerName": {"type": "string", "nullable": true}, "command": {"type": "string", "nullable": true}, "reason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PrintTicketDto": {"required": ["type"], "type": "object", "properties": {"type": {"minLength": 1, "type": "string"}, "printerName": {"type": "string", "nullable": true}, "licensePlate": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double", "nullable": true}, "paymentMethod": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "additionalData": {"type": "object", "additionalProperties": {}, "nullable": true}, "customContent": {"type": "string", "nullable": true}, "fontName": {"type": "string", "nullable": true}, "fontSize": {"type": "number", "format": "float"}, "useBold": {"type": "boolean"}}, "additionalProperties": false}, "PrinterInfoDto": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "isDefault": {"type": "boolean"}, "isOnline": {"type": "boolean"}, "status": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PrinterListResponseDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "error": {"type": "string", "nullable": true}, "printers": {"type": "array", "items": {"$ref": "#/components/schemas/PrinterInfoDto"}, "nullable": true}}, "additionalProperties": false}, "PrinterResponseDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "error": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "data": {"type": "object", "additionalProperties": {}, "nullable": true}}, "additionalProperties": false}, "PrinterStatusDto": {"type": "object", "properties": {"connected": {"type": "boolean"}, "status": {"type": "string", "nullable": true}, "printerName": {"type": "string", "nullable": true}, "lastError": {"type": "string", "nullable": true}, "lastPrintTime": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "StringDateTimeString<>f__AnonymousType1": {"type": "object", "properties": {"status": {"type": "string", "nullable": true}, "timestamp": {"type": "string", "format": "date-time"}, "version": {"type": "string", "nullable": true}}, "additionalProperties": false}}}}