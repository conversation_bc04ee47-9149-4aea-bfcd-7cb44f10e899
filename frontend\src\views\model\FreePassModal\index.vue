<template>
  <NModal
    :show="visible"
    preset="card"
    title="免费放行"
    :mask-closable="false"
    :closable="true"
    style="width: 500px;"
    @update:show="handleUpdateVisible"
    @close="handleClose"
  >
    <!-- 选项卡区域 -->
    <div class="tab-section">
      <div class="tab-buttons">
        <NButton
          :type="activeTab === 'leader' ? 'primary' : 'default'"
          :ghost="activeTab !== 'leader'"
          size="large"
          style="width: 150px; margin-right: 12px;"
          @click="activeTab = 'leader'"
        >
          领导同意免费
        </NButton>
        <NButton
          :type="activeTab === 'internal' ? 'primary' : 'default'"
          :ghost="activeTab !== 'internal'"
          size="large"
          style="width: 150px;"
          @click="activeTab = 'internal'"
        >
          内部车
        </NButton>
      </div>
    </div>

    <!-- 原因输入区域 -->
    <div class="reason-section">
      <div class="reason-label">自定义输入免费放行原因</div>
      <NInput
        :value="reason"
        @update:value="reason = $event"
        type="textarea"
        placeholder="请输入免费放行原因..."
        :rows="6"
        :maxlength="200"
        show-count
        style="margin-top: 8px;"
      />
    </div>

    <!-- 操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <NButton
          size="large"
          style="margin-right: 12px; width: 80px;"
          @click="handleCancel"
        >
          取消
        </NButton>
        <NButton
          type="primary"
          size="large"
          style="width: 80px;"
          :loading="loading"
          @click="handleConfirm"
        >
          确定
        </NButton>
      </div>
    </template>
  </NModal>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { NModal, NButton, NInput } from 'naive-ui'

defineOptions({
  name: 'FreePassModal'
})

// ==================== Props & Emits ====================
interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'confirm', data: { type: string; reason: string }): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false
})

const emit = defineEmits<Emits>()

// ==================== 响应式数据 ====================
const activeTab = ref<'leader' | 'internal'>('leader')
const reason = ref('')
const loading = ref(false)

// ==================== 方法 ====================
const handleUpdateVisible = (value: boolean) => {
  emit('update:visible', value)
}

const handleClose = () => {
  emit('update:visible', false)
}

const handleCancel = () => {
  emit('update:visible', false)
}

const handleConfirm = async () => {
  if (!reason.value.trim()) {
    // 这里可以添加提示消息
    return
  }

  loading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', {
      type: activeTab.value,
      reason: reason.value.trim()
    })
    
    emit('update:visible', false)
  } catch (error) {
    console.error('免费放行失败:', error)
  } finally {
    loading.value = false
  }
}

// ==================== 监听器 ====================
watch(() => props.visible, (newVal) => {
  if (newVal) {
    // 重置表单
    activeTab.value = 'leader'
    reason.value = ''
    loading.value = false
  }
})
</script>

<style scoped>
.tab-section {
  margin-bottom: 24px;
}

.tab-buttons {
  display: flex;
  justify-content: flex-start;
}

.reason-section {
  margin-bottom: 24px;
}

.reason-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  padding-top: 16px;
}
</style>
