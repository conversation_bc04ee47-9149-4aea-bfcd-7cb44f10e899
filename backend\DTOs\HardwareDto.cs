using System.ComponentModel.DataAnnotations;


namespace ParkingBoothApi.DTOs
{
    // 打印机状态DTO
    public class PrinterStatusDto
    {
        public bool Connected { get; set; }
        public string Status { get; set; } = "offline";
        public string? PrinterName { get; set; }
        public string? LastError { get; set; }
        public DateTime? LastPrintTime { get; set; }
    }

    // 打印票据请求DTO
    public class PrintTicketDto
    {
        [Required]
        public string Type { get; set; } = string.Empty; // "entry", "receipt", "test"

        public string? PrinterName { get; set; } // 指定打印机名称，为空则使用默认打印机
        public string? LicensePlate { get; set; }
        public decimal? Amount { get; set; }
        public string? PaymentMethod { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object>? AdditionalData { get; set; }

        // 打印内容相关
        public string? CustomContent { get; set; } // 自定义打印内容
        public string? FontName { get; set; } = "Arial"; // 字体名称
        public float FontSize { get; set; } = 12f; // 字体大小
        public bool UseBold { get; set; } = false; // 是否粗体
    }

    // 打开钱箱请求DTO
    public class OpenCashDrawerDto
    {
        public string? PrinterName { get; set; } // 指定打印机名称，为空则使用默认打印机
        public string? Command { get; set; } = "\x1B\x70\x00\x32\x32"; // ESC/POS命令，默认为标准开钱箱命令
        public string? Reason { get; set; } // 开钱箱原因
    }

    // 打印机响应DTO
    public class PrinterResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Error { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public Dictionary<string, object>? Data { get; set; }
    }

    // 钱箱响应DTO
    public class CashDrawerResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Error { get; set; }
        public DateTime Timestamp { get; set; } = DateTime.UtcNow;
        public string? PrinterUsed { get; set; }
    }

    // 打印机列表响应DTO
    public class PrinterListResponseDto
    {
        public bool Success { get; set; }
        public string Message { get; set; } = string.Empty;
        public string? Error { get; set; }
        public List<PrinterInfoDto> Printers { get; set; } = new();
    }

    // 打印机信息DTO
    public class PrinterInfoDto
    {
        public string Name { get; set; } = string.Empty;
        public bool IsDefault { get; set; }
        public bool IsOnline { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
