const { app, BrowserWindow, Menu, ipcMain, dialog } = require('electron')
const path = require('path')
const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged

// 保持对窗口对象的全局引用，如果不这么做的话，当JavaScript对象被
// 垃圾回收的时候，窗口会被自动地关闭
let mainWindow

function createWindow() {
  // 创建浏览器窗口
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets/icon.png'), // 应用图标
    title: 'HONGRUI 停车收费系统',
    show: false, // 先不显示，等加载完成后再显示
    titleBarStyle: 'default',
    frame: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    closable: true,
    fullscreen: true, // 默认全屏
    autoHideMenuBar: true // 自动隐藏菜单栏
  })

  // 窗口准备好后显示
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    // 开发环境下打开开发者工具
    if (isDev) {
      mainWindow.webContents.openDevTools()
    }
  })

  // 注册全局快捷键（停车岗亭系统专用）
  mainWindow.webContents.on('before-input-event', (event, input) => {
    // F11: 切换全屏
    if (input.key === 'F11' && input.type === 'keyDown') {
      mainWindow.setFullScreen(!mainWindow.isFullScreen())
    }
    // Ctrl+Shift+I: 开发者工具（仅开发环境）
    if (isDev && input.key === 'I' && input.control && input.shift && input.type === 'keyDown') {
      mainWindow.webContents.toggleDevTools()
    }
    // Ctrl+R: 刷新页面
    if (input.key === 'r' && input.control && input.type === 'keyDown') {
      mainWindow.webContents.reload()
    }
    // Alt+F4: 退出应用（Windows）
    if (process.platform === 'win32' && input.key === 'F4' && input.alt && input.type === 'keyDown') {
      app.quit()
    }
  })

  // 加载应用
  console.log('🔍 环境检测:')
  console.log('- NODE_ENV:', process.env.NODE_ENV)
  console.log('- app.isPackaged:', app.isPackaged)
  console.log('- isDev:', isDev)

  if (isDev) {
    // 开发环境：加载Vite开发服务器
    // 尝试常用端口
    const tryPorts = [5173, 5174]
    let devServerUrl = 'http://localhost:5173'

    // 检测可用端口
    for (const port of tryPorts) {
      try {
        const testUrl = `http://localhost:${port}`
        console.log(`🔍 检测端口 ${port}...`)
        // 这里简单使用第一个端口，实际运行时会自动重试
        devServerUrl = testUrl
        break
      } catch (error) {
        continue
      }
    }

    console.log('📡 加载开发服务器:', devServerUrl)
    mainWindow.loadURL(devServerUrl)

    // 添加错误处理
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
      console.error('❌ 页面加载失败:', errorCode, errorDescription, validatedURL)
      console.log('💡 请确保 Vue 开发服务器正在运行: npm run dev')

      // 尝试备用端口
      if (validatedURL.includes('5173')) {
        console.log('🔄 尝试备用端口 5174...')
        mainWindow.loadURL('http://localhost:5174')
      } else if (validatedURL.includes('5174')) {
        console.log('🔄 尝试默认端口 5173...')
        mainWindow.loadURL('http://localhost:5173')
      }
    })
  } else {
    // 生产环境：加载构建后的文件
    const fs = require('fs')
    const possiblePaths = [
      path.join(__dirname, '../dist/index.html'),
      path.join(__dirname, 'dist/index.html'),
      path.join(__dirname, '../../../dist/index.html'),
      path.join(process.resourcesPath, 'app/dist/index.html')
    ]

    // 写入调试日志
    const debugInfo = [
      `__dirname: ${__dirname}`,
      `process.resourcesPath: ${process.resourcesPath}`,
      `app.getAppPath(): ${app.getAppPath()}`,
      `app.isPackaged: ${app.isPackaged}`,
      ''
    ]

    let indexPath = null
    for (const testPath of possiblePaths) {
      const exists = fs.existsSync(testPath)
      debugInfo.push(`检测路径: ${testPath} - 存在: ${exists}`)
      if (exists) {
        indexPath = testPath
        break
      }
    }

    // 写入调试文件
    try {
      fs.writeFileSync(path.join(__dirname, 'debug.log'), debugInfo.join('\n'))
    } catch (e) {
      console.error('无法写入调试文件:', e)
    }

    if (indexPath) {
      console.log('✅ 找到index.html:', indexPath)
      mainWindow.loadFile(indexPath)
    } else {
      console.error('❌ 未找到index.html文件')
      // 显示错误页面
      mainWindow.loadURL('data:text/html,<h1>Error: Cannot find index.html</h1><p>Please check the application installation.</p>')
    }
  }

  // 当窗口被关闭时发出
  mainWindow.on('closed', () => {
    // 取消引用 window 对象，如果你的应用支持多窗口的话，
    // 通常会把多个 window 对象存放在一个数组里面，
    // 与此同时，你应该删除相应的元素。
    mainWindow = null
  })

  // 处理窗口最大化/还原
  mainWindow.on('maximize', () => {
    mainWindow.webContents.send('window-maximized')
  })

  mainWindow.on('unmaximize', () => {
    mainWindow.webContents.send('window-unmaximized')
  })
}

// Electron 会在初始化后并准备
// 创建浏览器窗口时，调用这个函数。
// 部分 API 在 ready 事件触发后才能使用。
app.whenReady().then(() => {
  createWindow()

  // 隐藏默认菜单
  Menu.setApplicationMenu(null)

  app.on('activate', () => {
    // 在macOS上，当单击dock图标并且没有其他窗口打开时，
    // 通常在应用程序中重新创建一个窗口。
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// 当全部窗口关闭时退出。
app.on('window-all-closed', () => {
  // 在 macOS 上，除非用户用 Cmd + Q 确定地退出，
  // 否则绝大部分应用及其菜单栏会保持激活。
  if (process.platform !== 'darwin') app.quit()
})

// 菜单已禁用 - 停车岗亭系统使用全屏模式，不需要传统菜单
// 如需要特殊功能，可通过应用内界面或快捷键实现

// IPC 通信处理
ipcMain.handle('get-app-version', () => {
  return app.getVersion()
})

ipcMain.handle('show-message-box', async (event, options) => {
  const result = await dialog.showMessageBox(mainWindow, options)
  return result
})

ipcMain.handle('show-save-dialog', async (event, options) => {
  const result = await dialog.showSaveDialog(mainWindow, options)
  return result
})

ipcMain.handle('show-open-dialog', async (event, options) => {
  const result = await dialog.showOpenDialog(mainWindow, options)
  return result
})

// 处理应用退出前的清理工作
app.on('before-quit', (event) => {
  // 可以在这里添加保存数据等清理工作
  console.log('应用即将退出，执行清理工作...')
})

// 处理未捕获的异常
process.on('uncaughtException', (error) => {
  console.error('未捕获的异常:', error)
  
  dialog.showErrorBox('应用错误', `发生了一个未预期的错误：\n\n${error.message}`)
})

// 在开发环境中启用热重载
if (isDev) {
  try {
    require('electron-reload')(__dirname, {
      electron: path.join(__dirname, '..', 'node_modules', '.bin', 'electron'),
      hardResetMethod: 'exit'
    })
  } catch (err) {
    console.log('electron-reload 未安装，跳过热重载功能')
  }
}
