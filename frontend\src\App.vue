<script setup lang="ts">
import { RouterView } from 'vue-router'
import { onMounted, ref } from 'vue'
import { NConfigProvider, NMessageProvider, NDialogProvider, NNotificationProvider, zhCN, dateZhCN } from 'naive-ui'
// import NotificationSystem from './components/NotificationSystem.vue'
// import { notification } from './utils/notification'
// import { blueTheme } from './theme/blue-theme'

const notificationRef = ref()

onMounted(() => {
  // 设置全局通知组件实例 (临时注释)
  // if (notificationRef.value) {
  //   notification.setNotificationComponent(notificationRef.value)
  // }
})

// 性能监控相关代码已移除
</script>

<template>
  <div id="app">
    <NConfigProvider :locale="zhCN" :date-locale="dateZhCN">
      <NMessageProvider>
        <NDialogProvider>
          <NNotificationProvider>
            <RouterView />
            <!-- 全局通知系统 (临时注释) -->
            <!-- <NotificationSystem ref="notificationRef" /> -->
          </NNotificationProvider>
        </NDialogProvider>
      </NMessageProvider>
    </NConfigProvider>
  </div>
</template>

<style>
/* Global styles for full-page layout */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Microsoft YaHei', 'PingFang SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: #f0f2f5;
  overflow: hidden;
}

#app {
  position: relative;
  height: 100vh;
  width: 100vw;
  overflow: hidden;
}

/* Global styles for parking booth system */
:global(body) {
  margin: 0;
  padding: 0;
  background-color: #f0f2f5;
}

:global(.btn-primary) {
  background-color: #1976d2;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

:global(.btn-primary:hover) {
  background-color: #1565c0;
}

:global(.btn-secondary) {
  background-color: #757575;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

:global(.btn-secondary:hover) {
  background-color: #616161;
}

:global(.card) {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 20px;
}

:global(.status-indicator) {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

:global(.status-online) {
  background-color: #4caf50;
}

:global(.status-offline) {
  background-color: #f44336;
}

:global(.status-warning) {
  background-color: #ff9800;
}
</style>
